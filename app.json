{"code": 200, "message": "success", "data": [{"id": 474, "name": "fs-qixin-cloud", "remark": "专属云企信业务合并服务", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L1", "mainOwner": "梁岩超Charles", "owners": ["尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>", "梁岩超Charles"], "category": "前台服务"}, {"id": 582, "name": "fs-wechat-sender-task", "remark": "微联服务号定时任务", "department": "互联业务互联平台组", "orgs": [], "level": "L2", "mainOwner": "封金运fengjy", "owners": ["钟兴ZhongXing", "张诚fine", "韦宁宁Jack<PERSON><PERSON>"], "category": ""}, {"id": 2459, "name": "checkins-office-v2-server-gray", "remark": "考勤签到服务 专属云灰度使用", "department": "消费品业务技术外勤组", "orgs": [], "level": "L2", "mainOwner": "张世民", "owners": ["张世民", "贺政忠HeZhengzhong", "何静媛hejy", "郭思卿"], "category": ""}, {"id": 103, "name": "fs-bi-permission", "remark": "bi新权限服务", "department": "数据业务技术BI组", "orgs": ["销售业务-BI"], "level": "L2", "mainOwner": "翟付杰Jeffrey", "owners": ["翟付杰Jeffrey", "陈江飞chenjiangfei"], "category": ""}, {"id": 2419, "name": "fs-expression-provider", "remark": "表情包", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L2", "mainOwner": "<PERSON>硕<PERSON><PERSON>", "owners": ["尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>", "梁岩超Charles"], "category": "前台服务"}, {"id": 326, "name": "fs-open-assets-web", "remark": "服务号-图片系统", "department": "制造行业开发组2", "orgs": [], "level": "L2", "mainOwner": "李秋林<PERSON>", "owners": ["李秋林<PERSON>"], "category": ""}, {"id": 266, "name": "fs-leica-index", "remark": "crm查重服务", "department": "平台运营架构组", "orgs": [], "level": "L2", "mainOwner": "谷广田", "owners": ["谷广田"], "category": ""}, {"id": 2364, "name": "oauth-center", "remark": "认证中心，提供用户登录验证，同时为外部应用提供getUserInfo和getAll接口获取用户信息", "department": "平台运营架构组", "orgs": [], "level": "L3", "mainOwner": "吴志辉<PERSON>Z<PERSON>hui", "owners": ["吴志辉<PERSON>Z<PERSON>hui", "李锐colin"], "category": ""}, {"id": 32, "name": "checkins-v2-task", "remark": "新版外勤定时任务", "department": "消费品业务技术外勤组", "orgs": ["快销农牧行业组"], "level": "L2", "mainOwner": "张世民", "owners": ["贺政忠HeZhengzhong", "何静媛hejy", "郭思卿", "张世民"], "category": ""}, {"id": 219, "name": "fs-flow-session", "remark": "流程服务号提醒服务", "department": "PaaS业务基础平台流程组", "orgs": [], "level": "L3", "mainOwner": "万松<PERSON>", "owners": ["万松<PERSON>"], "category": ""}, {"id": 2411, "name": "hamster-server", "remark": "hamster迁移工具", "department": "PaaS业务基础平台元数据组", "orgs": [], "level": "L3", "mainOwner": "梁梓闻liang<PERSON><PERSON>", "owners": ["杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian"], "category": "后台异步服务"}, {"id": 630, "name": "log-level-manager", "remark": "日志级别调整", "department": "平台运营架构组", "orgs": [], "level": "L3", "mainOwner": "吴志辉<PERSON>Z<PERSON>hui", "owners": ["李锐colin", "吴志辉<PERSON>Z<PERSON>hui"], "category": ""}, {"id": 214, "name": "fs-feeds-search-datamgr-update", "remark": "主站feeds搜索", "department": "深研营销业务部", "orgs": [], "level": "", "mainOwner": "郑辉Harry", "owners": ["郑辉Harry"], "category": ""}, {"id": 307, "name": "fs-notice", "remark": "强制通知", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L2", "mainOwner": "<PERSON>硕<PERSON><PERSON>", "owners": ["王毅Wang<PERSON>i", "梁岩超Charles", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 333, "name": "fs-open-jobs-executor", "remark": "应用中心任务调度", "department": "制造行业开发组2", "orgs": [], "level": "L2", "mainOwner": "李秋林<PERSON>", "owners": ["李秋林<PERSON>"], "category": "后台异步服务"}, {"id": 451, "name": "fs-plat-sandbox-provider", "remark": "沙箱服务", "department": "PaaS业务业务平台开发平台组", "orgs": ["销售协同-平台业务部"], "level": "L2", "mainOwner": "王亚豪Thorne", "owners": ["吴俊文Raymond", "王亚豪Thorne", "丁龙飞<PERSON><PERSON>"], "category": "前台服务"}, {"id": 156, "name": "fs-crm-sfa", "remark": "crm-SFA", "department": "SFA业务技术组", "orgs": ["互联业务-订货业务组", "平台运营-平台架构部", "平台运营-PaaS", "销售业务-自定义对象", "销售业务-SFA", "QA"], "level": "L0", "mainOwner": "赵鹏欣Paul", "owners": ["赵鹏欣Paul", "任林波Ren<PERSON><PERSON>bo", "岳朋远", "龚春如", "吉明哲daemon", "高光荣gaoguangrong", "孙得育", "陈金典Kimd"], "category": "前台服务"}, {"id": 237, "name": "fs-implementation-task", "remark": "负责人： liyg", "department": "PaaS业务业务平台部", "orgs": [], "level": "", "mainOwner": "王毅Wang<PERSON>i", "owners": [], "category": "前台服务"}, {"id": 283, "name": "fs-marketing-statistic-provider", "remark": "营销通统计服务", "department": "营销业务营销业务组", "orgs": ["互联业务-客脉业务组", "互联业务-互联平台组"], "level": "L1", "mainOwner": "周浮洋bruce", "owners": ["郑辉Harry", "罗勇Pate", "张树锋Halcyon", "周浮洋bruce", "张玉佳Mike"], "category": "前台服务"}, {"id": 2465, "name": "fs-oncall", "remark": "告警", "department": "平台运营架构组", "orgs": [], "level": "L2", "mainOwner": "张恕征Zhang<PERSON>g", "owners": ["张恕征Zhang<PERSON>g", "吴志辉<PERSON>Z<PERSON>hui"], "category": ""}, {"id": 308, "name": "fs-oauth-base-provider", "remark": "开平oauth服务", "department": "互联业务互联平台组", "orgs": [], "level": "L2", "mainOwner": "刘诗林liushilin", "owners": ["刘诗林liushilin", "张诚fine", "曾令文Evan", "钟兴ZhongXing"], "category": ""}, {"id": 325, "name": "fs-open-assets-upload", "remark": "应用中心静态资源上传服务", "department": "制造行业开发组2", "orgs": [], "level": "L2", "mainOwner": "李秋林<PERSON>", "owners": ["李秋林<PERSON>"], "category": ""}, {"id": 362, "name": "fs-paas-calculate-task", "remark": "计算、统计字段计算服务", "department": "PaaS业务业务平台基础对象组", "orgs": ["销售业务-自定义对象"], "level": "L1", "mainOwner": "周伟荣", "owners": ["周伟荣", "赵琚", "郑磊<PERSON><PERSON><PERSON>", "冯津<PERSON><PERSON><PERSON>", "王毅Wang<PERSON>i"], "category": "后台异步服务"}, {"id": 463, "name": "fs-polling-cgi", "remark": "轮询接口", "department": "平台运营架构组", "orgs": [], "level": "L0", "mainOwner": "谷广田", "owners": ["刘文绪dio", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 118, "name": "fs-bpm", "remark": "BPM服务", "department": "PaaS业务基础平台流程组", "orgs": ["平台运营-PaaS"], "level": "L1", "mainOwner": "梁楠liangnan", "owners": ["万松<PERSON>", "崔永旭", "梁楠liangnan"], "category": "前台服务"}, {"id": 124, "name": "fs-bpm-processor", "remark": "业务流消息消费", "department": "PaaS业务基础平台流程组", "orgs": [], "level": "L1", "mainOwner": "梁楠liangnan", "owners": ["郑子阳Julian", "崔永旭", "万松<PERSON>", "梁楠liangnan", "刘畅CharonLucca"], "category": "后台异步服务"}, {"id": 148, "name": "fs-crm-import-sfa", "remark": "SFA-import", "department": "SFA业务技术售中业财组", "orgs": [], "level": "L2", "mainOwner": "任林波Ren<PERSON><PERSON>bo", "owners": ["刘勋", "任林波Ren<PERSON><PERSON>bo", "赵鹏欣Paul", "岳朋远", "龚春如", "吉明哲daemon", "陈金典Kimd"], "category": "前台服务"}, {"id": 174, "name": "fs-dataplatform-lead-score-online", "remark": "线索打分服务", "department": "数据业务技术平台组", "orgs": [], "level": "", "mainOwner": "纪二飞Roy", "owners": ["纪二飞Roy"], "category": ""}, {"id": 2420, "name": "fs-qixin-web-check", "remark": "web轮训", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L2", "mainOwner": "梁岩超Charles", "owners": ["尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>", "梁岩超Charles"], "category": "前台服务"}, {"id": 2520, "name": "fs-paas-function-service-runtime-02", "remark": "函数服务", "department": "PaaS业务业务平台开发平台组", "orgs": ["销售业务-自定义对象", "销售协同-平台业务部"], "level": "L2", "mainOwner": "吴俊文Raymond", "owners": ["吴俊文Raymond", "斯作益seth"], "category": "前台服务"}, {"id": 126, "name": "fs-bug-feedback", "remark": "在线bug反馈", "department": "平台运营架构组", "orgs": [], "level": "L3", "mainOwner": "谷广田", "owners": ["谷广田"], "category": ""}, {"id": 151, "name": "fs-crm-metadata", "remark": "CRM Java", "department": "SFA业务技术售中技术组", "orgs": [], "level": "L2", "mainOwner": "任林波Ren<PERSON><PERSON>bo", "owners": ["赵鹏欣Paul", "任林波Ren<PERSON><PERSON>bo", "陈金典Kimd"], "category": "后台异步服务"}, {"id": 309, "name": "fs-oauth-base-web", "remark": "开放平台", "department": "互联业务互联平台组", "orgs": [], "level": "L2", "mainOwner": "刘诗林liushilin", "owners": ["刘诗林liushilin", "张诚fine", "曾令文Evan", "钟兴ZhongXing"], "category": ""}, {"id": 2540, "name": "fs-paas-action-centre", "remark": "fs-paas-action-centre", "department": "PaaS业务业务平台开发平台组", "orgs": [], "level": "", "mainOwner": "张晓峰zxf", "owners": ["张晓峰zxf", "吴俊文Raymond"], "category": ""}, {"id": 222, "name": "fs-fmcg-efficiency-cgi", "remark": "快消客开-审核服务-urgent环境", "department": "消费品业务技术外勤组", "orgs": ["快销农牧行业组"], "level": "L2", "mainOwner": "贺政忠HeZhengzhong", "owners": ["杨庆飞", "贺政忠HeZhengzhong", "杨亚兴", "林明杰", "吴永鑫", "张常清zhangchangqing"], "category": ""}, {"id": 383, "name": "fs-paas-refresh-forest", "remark": "刷库程序（多团队共用，按需添加代码）", "department": "PaaS业务基础平台元数据组", "orgs": ["平台运营-PaaS", "互联业务-订货业务组"], "level": "L3", "mainOwner": "杨华国YHG", "owners": ["杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian"], "category": "后台异步服务"}, {"id": 2499, "name": "fs-sail-order-cloud", "remark": "新版订货通专属云沙盒", "department": "互联业务订货业务组", "orgs": [], "level": "L2", "mainOwner": "陈柱深", "owners": ["袁杰<PERSON><PERSON>d"], "category": "前台服务"}, {"id": 665, "name": "weex-console-service", "remark": "小程序发布和数据更新管理系统，主要用于根据企业ei + ei(支持灰度)下发小程序包的CDN地址。如果此服务异常，新安装纷享app会白屏，新版本发布的场景，新的数据包无法下发，相当于发版失败。", "department": "平台运营架构组", "orgs": [], "level": "L2", "mainOwner": "侯世鹏", "owners": ["侯世鹏"], "category": "前台服务"}, {"id": 77, "name": "fs-bi-billboard-server", "remark": "BI看板服务", "department": "数据业务技术平台组", "orgs": [], "level": "L2", "mainOwner": "纪二飞Roy", "owners": ["纪二飞Roy"], "category": ""}, {"id": 129, "name": "fs-cep-provider", "remark": "CEP服务，告警请看CEP慢日志，联系业务负责人", "department": "PaaS业务业务平台开发平台组", "orgs": ["销售协同-平台业务部"], "level": "L0", "mainOwner": "吴俊文Raymond", "owners": ["李锐colin", "吴俊文Raymond", "王亚豪Thorne"], "category": "前台服务"}, {"id": 180, "name": "fs-db-copier", "remark": "数据迁移服务", "department": "PaaS业务基础平台元数据组", "orgs": [], "level": "L2", "mainOwner": "梁梓闻liang<PERSON><PERSON>", "owners": ["梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian", "李锐colin"], "category": "后台异步服务"}, {"id": 2539, "name": "fs-paas-resource", "remark": "统计ai资源服务", "department": "PaaS业务业务平台开发平台组", "orgs": [], "level": "L2", "mainOwner": "陈明宇chenmingyu", "owners": ["斯作益seth", "吴俊文Raymond", "陈明宇chenmingyu"], "category": "后台异步服务"}, {"id": 2353, "name": "fs-plat-auth-task", "remark": "功能权限异步处理", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L2", "mainOwner": "刘文绪dio", "owners": ["张文超", "刘文绪dio", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "买年顺"], "category": "后台异步服务"}, {"id": 2470, "name": "fs-plat-organization-provider-cloud", "remark": "--", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "", "mainOwner": "刘文绪dio", "owners": ["刘文绪dio", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "category": ""}, {"id": 454, "name": "fs-plat-service-provider", "remark": "社交组老服务合并", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L2", "mainOwner": "刘文绪dio", "owners": ["刘文绪dio", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 491, "name": "fs-qixin-web", "remark": "企信WEB端业务入口", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L1", "mainOwner": "梁岩超Charles", "owners": ["梁岩超Charles", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 75, "name": "fs-appserver-trigger-provider", "remark": "外部积分触发器MONGO访问", "department": "消费品业务技术销费组", "orgs": [], "level": "L2", "mainOwner": "杨庆飞", "owners": ["杨庆飞"], "category": "后台异步服务"}, {"id": 147, "name": "fs-crm-import", "remark": "fs-crm独立出的导入导出服务", "department": "PaaS业务业务平台基础对象组", "orgs": ["销售业务-自定义对象", "互联业务-互联应用组", "平台运营-平台架构部", "平台运营-PaaS", "销售业务-SFA", "互联业务-服务业务组"], "level": "L2", "mainOwner": "冯津<PERSON><PERSON><PERSON>", "owners": ["郑磊<PERSON><PERSON><PERSON>", "周伟荣", "赵琚", "王毅Wang<PERSON>i", "冯津<PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 291, "name": "fs-message-server", "remark": "消息平台", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L1", "mainOwner": "<PERSON>硕<PERSON><PERSON>", "owners": ["尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>", "梁岩超Charles"], "category": "前台服务"}, {"id": 392, "name": "fs-paas-workflow", "remark": "Workflow的灰度机器,主要支持CRM,BPM和协同审批流的灰度", "department": "PaaS业务基础平台流程组", "orgs": ["平台运营-PaaS", "销售业务-流程组"], "level": "L0", "mainOwner": "万松<PERSON>", "owners": ["郑子阳Julian", "万松<PERSON>", "崔永旭", "宁天伟Ethan"], "category": "前台服务"}, {"id": 23, "name": "auditlog-query-service", "remark": "应用日志审计服务", "department": "PaaS业务业务平台基础对象组", "orgs": ["销售业务-自定义对象"], "level": "L2", "mainOwner": "李远", "owners": ["王毅Wang<PERSON>i", "郑磊<PERSON><PERSON><PERSON>", "李远"], "category": "前台服务"}, {"id": 39, "name": "crm-bi-custom-statistic-online", "remark": "老自定义统计图实时计算灰度环境", "department": "数据业务技术平台组", "orgs": [], "level": "L2", "mainOwner": "纪二飞Roy", "owners": ["纪二飞Roy", "翟付杰Jeffrey", "赵孟华HuaZai", "郑子阳Julian"], "category": ""}, {"id": 79, "name": "fs-bi-cloud", "remark": "BI服务", "department": "数据业务技术BI组", "orgs": [], "level": "L1", "mainOwner": "翟付杰Jeffrey", "owners": ["翟付杰Jeffrey", "纪二飞Roy"], "category": "前台服务"}, {"id": 132, "name": "fs-chconnector-provider", "remark": "特变电工私有云对接", "department": "消费品业务技术销费组", "orgs": ["快销农牧行业组"], "level": "L2", "mainOwner": "杨庆飞", "owners": ["杨庆飞"], "category": ""}, {"id": 657, "name": "sfa-console", "remark": "sfa 业务平台", "department": "SFA业务售前组", "orgs": [], "level": "L3", "mainOwner": "李坤bruce", "owners": [], "category": ""}, {"id": 2307, "name": "fs-collector-clientlog", "remark": "终端设备日志转发日志中心", "department": "平台运营运维组", "orgs": [], "level": "L3", "mainOwner": "谷广田", "owners": ["谷广田", "吴志辉<PERSON>Z<PERSON>hui"], "category": ""}, {"id": 2537, "name": "fs-crm-sfa-rest", "remark": "sfa服务承接内部请求用", "department": "SFA业务技术售中业财组", "orgs": [], "level": "L0", "mainOwner": "任林波Ren<PERSON><PERSON>bo", "owners": ["任林波Ren<PERSON><PERSON>bo"], "category": "前台服务"}, {"id": 473, "name": "fs-qixin-business-broker", "remark": "审批提醒，CRM提醒消票数等业务broker", "department": "PaaS业务业务平台协同业务组", "orgs": ["销售协同-企信"], "level": "L2", "mainOwner": "梁岩超Charles", "owners": ["梁岩超Charles", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>"], "category": "后台异步服务"}, {"id": 624, "name": "index-server", "remark": "表单索引服务", "department": "平台运营运维组", "orgs": [], "level": "", "mainOwner": "李锐colin", "owners": ["李锐colin"], "category": ""}, {"id": 253, "name": "fs-ka-crawler-zhongken", "remark": "中垦一期华润KA系统数据爬取程序 负责人： 徐超,徐瑶佳,李清波", "department": "集成开发组", "orgs": [], "level": "", "mainOwner": "凌钦羽", "owners": [], "category": ""}, {"id": 275, "name": "fs-mankeep-outweb", "remark": "客脉项目客脉web端管理，早期服务，现在不需要，服务已经下线", "department": "深研营销业务部", "orgs": ["互联业务-客脉业务组"], "level": "", "mainOwner": "郑辉Harry", "owners": ["郑辉Harry", "张胜斌zhangshengbin"], "category": ""}, {"id": 2384, "name": "fs-document-convert", "remark": "文档转换服务", "department": "平台运营架构组", "orgs": [], "level": "L2", "mainOwner": "安宜龙Andy", "owners": ["安宜龙Andy", "刘云松samuel"], "category": ""}, {"id": 200, "name": "fs-esign-connector", "remark": "e签宝对接项目", "department": "SFA业务技术售中技术组", "orgs": [], "level": "L3", "mainOwner": "刘勋", "owners": ["刘勋"], "category": ""}, {"id": 207, "name": "fs-feeds-adapter", "remark": "Java协同服务", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "", "mainOwner": "张文超", "owners": ["尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "张文超"], "category": ""}, {"id": 2544, "name": "fs-flow-vote", "remark": "流程fs-flow清除票数使用独立部署服务", "department": "PaaS业务基础平台流程组", "orgs": [], "level": "L1", "mainOwner": "万松<PERSON>", "owners": [], "category": "前台服务"}, {"id": 264, "name": "fs-landray-oa-provider", "remark": "蓝凌OA对接CRM服务_陈祥飞", "department": "平台运营运维组", "orgs": ["互联业务-开平对接组", "互联业务-产品合作组"], "level": "", "mainOwner": "吴永佳yongjia", "owners": ["吴永佳yongjia"], "category": ""}, {"id": 370, "name": "fs-paas-function-service-runtime-provider", "remark": "函数", "department": "PaaS业务业务平台开发平台组", "orgs": ["销售业务-自定义对象", "销售协同-平台业务部"], "level": "L0", "mainOwner": "斯作益seth", "owners": ["斯作益seth", "刘一鸣Adam", "吴俊文Raymond"], "category": "后台异步服务"}, {"id": 539, "name": "fs-tbeagroup-connector", "remark": "特变云免登陆认证服务", "department": "实施运营组", "orgs": [], "level": "", "mainOwner": "曾虎彪", "owners": [], "category": ""}, {"id": 547, "name": "fs-tuosida-print", "remark": "拓斯达客开-报价单打印", "department": "制造行业客开组", "orgs": [], "level": "L3", "mainOwner": "颜彩朵", "owners": ["颜彩朵"], "category": ""}, {"id": 34, "name": "cloud-mq-proxy", "remark": "跨云MQ消息投递，mq消息接收服务.只提供了一个sends方法，用于接受mq消息，然后把接收到的mq消息发送到指定的集群和topic,如果此服务异常，会导致投递的mq消费无法投递到指定的目标环境", "department": "平台运营架构组", "orgs": [], "level": "L2", "mainOwner": "侯世鹏", "owners": ["侯世鹏"], "category": "后台异步服务"}, {"id": 67, "name": "fs-appserver-openproxy-web", "remark": "开平代理服务-战报助手用", "department": "消费品业务技术组", "orgs": ["快销农牧行业组"], "level": "L2", "mainOwner": "贺政忠HeZhengzhong", "owners": ["贺政忠HeZhengzhong"], "category": ""}, {"id": 2549, "name": "fs-fmcg-ai-server", "remark": "消费品访销ai服务", "department": "消费品业务技术外勤组", "orgs": [], "level": "L1", "mainOwner": "郭思卿", "owners": [], "category": ""}, {"id": 1, "name": "fs-k8s-tomcat-test", "remark": "k8s下tomcat应用测试  ", "department": "平台运营架构组", "orgs": [], "level": "L3", "mainOwner": "吴志辉<PERSON>Z<PERSON>hui", "owners": ["吴志辉<PERSON>Z<PERSON>hui"], "category": ""}, {"id": 2483, "name": "unique-index-service", "remark": "表单唯一性索引服务", "department": "平台运营架构组", "orgs": [], "level": "", "mainOwner": "李锐colin", "owners": ["李锐colin"], "category": ""}, {"id": 571, "name": "fs-warehouse-provider", "remark": "文件服务dubbo、rest接口", "department": "平台运营架构组", "orgs": [], "level": "L2", "mainOwner": "安宜龙Andy", "owners": ["安宜龙Andy"], "category": ""}, {"id": 161, "name": "fs-crm-template", "remark": "crm模版", "department": "PaaS业务业务平台基础对象组", "orgs": [], "level": "L2", "mainOwner": "赵琚", "owners": ["郑磊<PERSON><PERSON><PERSON>", "王毅Wang<PERSON>i", "周伟荣", "赵琚", "冯津<PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 2433, "name": "fs-db-migrator", "remark": "", "department": "SFA业务技术售中业财组", "orgs": [], "level": "L2", "mainOwner": "任林波Ren<PERSON><PERSON>bo", "owners": ["任林波Ren<PERSON><PERSON>bo"], "category": "前台服务"}, {"id": 255, "name": "fs-ka-regularization-zhongken", "remark": "KA数据规整化 负责人： 李清波，徐超，徐瑶佳Gaven", "department": "集成开发组", "orgs": [], "level": "", "mainOwner": "凌钦羽", "owners": [], "category": ""}, {"id": 2444, "name": "fs-paas-wishful-cloud", "remark": "NPS，OCR，函数模板，电子签功能合集", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L2", "mainOwner": "<PERSON>硕<PERSON><PERSON>", "owners": ["李远", "<PERSON>硕<PERSON><PERSON>", "梁岩超Charles", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "category": ""}, {"id": 588, "name": "fs-workflow-processor", "remark": "工作流消息处理", "department": "PaaS业务基础平台流程组", "orgs": [], "level": "L1", "mainOwner": "刘畅CharonLucca", "owners": ["崔永旭", "万松<PERSON>", "宁天伟Ethan", "梁楠liangnan", "刘畅CharonLucca"], "category": "后台异步服务"}, {"id": 187, "name": "fs-dirty-words", "remark": "脏词（敏感词）校验服务 @吴志辉 ", "department": "", "orgs": [], "level": "", "mainOwner": "", "owners": [], "category": ""}, {"id": 189, "name": "fs-document-preview", "remark": "文档预览", "department": "平台运营架构组", "orgs": [], "level": "L2", "mainOwner": "安宜龙Andy", "owners": ["安宜龙Andy", "刘云松samuel"], "category": ""}, {"id": 508, "name": "fs-service-console", "remark": "公司java服务控制台 公共服务", "department": "互联业务订货业务组", "orgs": [], "level": "L3", "mainOwner": "万松<PERSON>", "owners": ["万松<PERSON>"], "category": "合并部署服务"}, {"id": 520, "name": "fs-stone-audioserver", "remark": "stone语音转换", "department": "平台运营架构组", "orgs": [], "level": "L2", "mainOwner": "安宜龙Andy", "owners": ["安宜龙Andy"], "category": ""}, {"id": 176, "name": "fs-dataplatform-metrics-producer", "remark": "核心指标上报服务", "department": "数据业务技术平台组", "orgs": [], "level": "L3", "mainOwner": "赵孟华HuaZai", "owners": ["赵孟华HuaZai"], "category": ""}, {"id": 471, "name": "fs-qixin-bot-manage", "remark": "企信助手管理服务", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L2", "mainOwner": "梁岩超Charles", "owners": ["王毅Wang<PERSON>i", "梁岩超Charles", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>"], "category": "前台服务"}, {"id": 596, "name": "hubble-wal-log", "remark": "数据变更流", "department": "北研业务线", "orgs": [], "level": "L0", "mainOwner": "杨华国YHG", "owners": ["杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian"], "category": "后台异步服务"}, {"id": 130, "name": "fs-chang<PERSON>i", "remark": "常柴发货通知单批量打印客开项目", "department": "制造行业客开组", "orgs": [], "level": "", "mainOwner": "颜彩朵", "owners": [], "category": ""}, {"id": 541, "name": "fs-todo", "remark": "待办服务", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L2", "mainOwner": "<PERSON>硕<PERSON><PERSON>", "owners": ["梁岩超Charles", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>"], "category": "前台服务"}, {"id": 2429, "name": "socketlog-service", "remark": "客户端日志接收", "department": "平台运营架构组", "orgs": [], "level": "L3", "mainOwner": "谷广田", "owners": ["谷广田", "吴志辉<PERSON>Z<PERSON>hui"], "category": ""}, {"id": 85, "name": "fs-bi-custom-statistic-offline", "remark": "自定义统计图离线计算灰度环境", "department": "数据业务技术平台组", "orgs": ["销售业务-BI", "平台运营-平台架构部"], "level": "L2", "mainOwner": "纪二飞Roy", "owners": ["纪二飞Roy", "赵孟华HuaZai", "郑子阳Julian"], "category": ""}, {"id": 2434, "name": "fs-crm-fmcg-sales", "remark": "订货crm服务", "department": "消费品业务技术创新组", "orgs": [], "level": "L1", "mainOwner": "杨亚兴", "owners": ["田洪振Hongzhen", "杨亚兴", "贺政忠HeZhengzhong"], "category": "前台服务"}, {"id": 301, "name": "fs-metadata-reference", "remark": "元数据引用关系", "department": "PaaS业务基础平台元数据组", "orgs": [], "level": "L2", "mainOwner": "钱凌锋irony", "owners": ["杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian"], "category": "后台异步服务"}, {"id": 523, "name": "fs-stone-cloud", "remark": "企业文件（stone）数据专属存储服务", "department": "平台运营架构组", "orgs": [], "level": "L0", "mainOwner": "安宜龙Andy", "owners": ["安宜龙Andy"], "category": "合并部署服务"}, {"id": 86, "name": "fs-bi-custom-statistic-online-calculate", "remark": "老自定义统计图实时计算批量计算", "department": "数据业务技术平台组", "orgs": [], "level": "L2", "mainOwner": "纪二飞Roy", "owners": ["纪二飞Roy", "翟付杰Jeffrey", "赵孟华HuaZai", "郑子阳Julian"], "category": ""}, {"id": 216, "name": "fs-file-preview-service", "remark": "文件预览数据源服务层", "department": "平台运营架构组", "orgs": [], "level": "L2", "mainOwner": "安宜龙Andy", "owners": ["安宜龙Andy", "刘云松samuel"], "category": ""}, {"id": 299, "name": "fs-metadata-nginx-purge", "remark": "清理describe的nginx缓存", "department": "PaaS业务基础平台元数据组", "orgs": [], "level": "", "mainOwner": "钱凌锋irony", "owners": ["杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian"], "category": ""}, {"id": 2498, "name": "surey", "remark": "问卷", "department": "互联业务订货业务组", "orgs": [], "level": "L2", "mainOwner": "王凡", "owners": ["袁杰<PERSON><PERSON>d", "陈柱深"], "category": "前台服务"}, {"id": 149, "name": "fs-crm-integral", "remark": "CRM行为积分门面服务", "department": "SFA业务技术售中技术组", "orgs": [], "level": "L2", "mainOwner": "赵鹏欣Paul", "owners": ["赵鹏欣Paul"], "category": "后台异步服务"}, {"id": 2554, "name": "fs-crm-manufacturing-rest", "remark": "制造业fs-crm服务（承载后台rest调用）", "department": "制造行业开发组2", "orgs": [], "level": "L1", "mainOwner": "李秋林<PERSON>", "owners": ["李秋林<PERSON>", "郭明Zero", "丁成<PERSON><PERSON>", "杨希luke"], "category": "前台服务"}, {"id": 185, "name": "fs-dingtalk-provider", "remark": "钉钉对接插件", "department": "互联业务集成平台组", "orgs": [], "level": "L1", "mainOwner": "冯院华Hardy", "owners": ["柯南颖", "冯院华"], "category": "后台异步服务"}, {"id": 436, "name": "fs-plat-fap", "remark": "fap长连接接入", "department": "PaaS业务业务平台开发平台组", "orgs": [], "level": "L0", "mainOwner": "吴俊文Raymond", "owners": ["吴俊文Raymond"], "category": "前台服务"}, {"id": 373, "name": "fs-paas-license", "remark": "paas-分版license服务", "department": "PaaS业务基础平台元数据组", "orgs": [], "level": "L0", "mainOwner": "李晨Jason", "owners": ["梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian"], "category": "前台服务"}, {"id": 2442, "name": "fs-process-service-task-set-for-cloud", "remark": "专为专属云合成的 流程服务集合 ; 业务层 1. 业务流程 2. 审批流程 3. 公共服务 4. 阶段推进器 5. 工作流程", "department": "PaaS业务基础平台流程组", "orgs": [], "level": "", "mainOwner": "万松<PERSON>", "owners": ["万松<PERSON>"], "category": ""}, {"id": 516, "name": "fs-stage-propeller", "remark": "阶段推进器灰度服务", "department": "PaaS业务基础平台流程组", "orgs": [], "level": "L1", "mainOwner": "梁楠liangnan", "owners": ["崔永旭", "万松<PERSON>", "宁天伟Ethan", "梁楠liangnan", "刘畅CharonLucca"], "category": "前台服务"}, {"id": 453, "name": "fs-plat-service-cloud", "remark": "管理后台人员部门对象化服务", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L2", "mainOwner": "刘文绪dio", "owners": ["张文超", "刘文绪dio", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 106, "name": "fs-bi-scheduler", "remark": "BI首页定时刷新服务", "department": "数据业务技术BI组", "orgs": [], "level": "L2", "mainOwner": "翟付杰Jeffrey", "owners": ["翟付杰Jeffrey"], "category": ""}, {"id": 286, "name": "fs-marketing-web-kis", "remark": "营销通kis服务", "department": "营销业务营销业务组", "orgs": ["互联业务-客脉业务组", "互联业务-互联平台组"], "level": "L1", "mainOwner": "郑辉Harry", "owners": ["郑辉Harry", "罗勇Pate", "张树锋Halcyon", "周浮洋bruce", "张玉佳Mike"], "category": "前台服务"}, {"id": 329, "name": "fs-open-email-proxy", "remark": "邮箱服务", "department": "深研制造行业部", "orgs": [], "level": "", "mainOwner": "谢向达", "owners": ["谢向达", "丁成<PERSON><PERSON>", "郭明Zero", "丁成<PERSON><PERSON>"], "category": ""}, {"id": 2352, "name": "fs-plat-auth-biz", "remark": "功能权限biz层", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L2", "mainOwner": "刘文绪dio", "owners": ["刘文绪dio", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 311, "name": "fs-online-consult-base", "remark": "华为云在线客服", "department": "制造行业开发组1", "orgs": [], "level": "L1", "mainOwner": "郭明Zero", "owners": ["丁成<PERSON><PERSON>", "郭明Zero", "戴云"], "category": "前台服务"}, {"id": 468, "name": "fs-qixin-biz-web", "remark": "企信移动端业务接入入口", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L1", "mainOwner": "梁岩超Charles", "owners": ["尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>", "梁岩超Charles"], "category": "前台服务"}, {"id": 87, "name": "fs-bi-custom-statistic-paas2bi-transfer", "remark": "目标goal_value_obj清洗服务", "department": "数据业务技术平台组", "orgs": [], "level": "L2", "mainOwner": "纪二飞Roy", "owners": ["纪二飞Roy", "赵正豪zhenghao", "赵孟华HuaZai"], "category": "后台异步服务"}, {"id": 2473, "name": "fs-coordination", "remark": "协同元数据", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "", "mainOwner": "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "owners": ["尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "category": ""}, {"id": 232, "name": "fs-hubble-cloud", "remark": "全局搜索查询,数据同步合并服务", "department": "PaaS业务基础平台元数据组", "orgs": [], "level": "L1", "mainOwner": "吕杰lvjie", "owners": ["杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian"], "category": "合并部署服务"}, {"id": 281, "name": "fs-marketing-statistic", "remark": "营销通统计服务", "department": "营销业务营销业务组", "orgs": [], "level": "L2", "mainOwner": "郑辉Harry", "owners": ["郑辉Harry"], "category": "前台服务"}, {"id": 19, "name": "attachment-search-broker-adapter", "remark": "主站附件搜索broker", "department": "深研营销业务部", "orgs": ["互联业务-客脉业务组"], "level": "", "mainOwner": "郑辉Harry", "owners": ["郑辉Harry"], "category": ""}, {"id": 38, "name": "crm-bi-custom-statistic-dim", "remark": "自定义统计图dim计算", "department": "数据业务技术平台组", "orgs": ["平台运营-平台架构部", "销售业务-BI"], "level": "", "mainOwner": "纪二飞Roy", "owners": ["纪二飞Roy", "赵孟华HuaZai"], "category": ""}, {"id": 209, "name": "fs-feeds-notice", "remark": "Feed通知服务", "department": "PaaS业务业务平台协同业务组", "orgs": ["销售协同-平台业务部"], "level": "L2", "mainOwner": "张文超", "owners": ["张文超", "刘文绪dio", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 659, "name": "svc-checkins-office-provider", "remark": "老考勤数据服务层服务", "department": "平台运营运维组", "orgs": ["快销农牧行业组"], "level": "L2", "mainOwner": "张世民", "owners": ["贺政忠HeZhengzhong", "郭思卿", "张世民"], "category": ""}, {"id": 257, "name": "fs-kis-connector-provider", "remark": "金蝶KIS对接应用服务层", "department": "平台运营运维组", "orgs": ["互联业务-开平对接组", "互联业务-客脉业务组", "互联业务-产品合作组"], "level": "L2", "mainOwner": "冯院华Hardy", "owners": ["冯院华Hardy", "柯南颖"], "category": ""}, {"id": 2576, "name": "fs-open-email-proxy-task-provider", "remark": "邮箱服务", "department": "制造行业开发组1", "orgs": [], "level": "L2", "mainOwner": "谢向达", "owners": ["谢向达", "丁成<PERSON><PERSON>"], "category": "前台服务"}, {"id": 480, "name": "fs-qixin-provider", "remark": "企信核心业务服务", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L1", "mainOwner": "梁岩超Charles", "owners": ["尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>", "梁岩超Charles"], "category": "前台服务"}, {"id": 89, "name": "fs-bi-devops", "remark": "BI-企业健康度检测工具", "department": "数据业务技术BI组", "orgs": [], "level": "L3", "mainOwner": "翟付杰Jeffrey", "owners": ["翟付杰Jeffrey", "纪二飞Roy", "陈江飞chenjiangfei"], "category": ""}, {"id": 144, "name": "fs-crm", "remark": "自定义对象", "department": "互联业务订货业务组", "orgs": [], "level": "L1", "mainOwner": "袁杰<PERSON><PERSON>d", "owners": ["郑磊<PERSON><PERSON><PERSON>", "周伟荣", "赵琚", "李磊Leo", "郑子阳Julian", "钟兴ZhongXing", "袁杰<PERSON><PERSON>d", "王凡", "郑辉Harry"], "category": "前台服务"}, {"id": 341, "name": "fs-open-operation-support", "remark": "开放平台操作管理", "department": "数据业务技术平台组", "orgs": [], "level": "", "mainOwner": "钟兴ZhongXing", "owners": ["钟兴ZhongXing"], "category": ""}, {"id": 472, "name": "fs-qixin-bot-provider", "remark": "企信助手订阅服务", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L2", "mainOwner": "梁岩超Charles", "owners": ["尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>", "梁岩超Charles"], "category": "前台服务"}, {"id": 184, "name": "fs-dingtalk-client", "remark": "钉钉对接产品中转client。负责人：李清波，徐瑶佳", "department": "互联业务集成平台组", "orgs": [], "level": "L3", "mainOwner": "柯南颖", "owners": [], "category": "后台异步服务"}, {"id": 2523, "name": "fs-huawei-kit-web", "remark": "华为kit对接", "department": "互联业务集成平台组", "orgs": [], "level": "L1", "mainOwner": "陈宗鑫chenzongxin", "owners": [], "category": "前台服务"}, {"id": 314, "name": "fs-online-consult-externals", "remark": "华为云在线客服", "department": "制造行业开发组1", "orgs": [], "level": "L2", "mainOwner": "戴云", "owners": ["戴云"], "category": ""}, {"id": 2521, "name": "fs-paas-function-service-runtime-03", "remark": "函数服务", "department": "PaaS业务业务平台开发平台组", "orgs": ["销售业务-自定义对象", "销售协同-平台业务部"], "level": "", "mainOwner": "斯作益seth", "owners": ["斯作益seth", "吴俊文Raymond"], "category": ""}, {"id": 221, "name": "fs-fmcg-customized-excel", "remark": "快消行业-客开-报表服务", "department": "消费品业务技术创新组", "orgs": ["快销农牧行业组"], "level": "L2", "mainOwner": "贺政忠HeZhengzhong", "owners": ["贺政忠HeZhengzhong", "杨庆飞", "杨亚兴", "郭思卿"], "category": ""}, {"id": 245, "name": "fs-job-admin", "remark": "任务调度服务", "department": "平台运营架构组", "orgs": [], "level": "L1", "mainOwner": "谷广田", "owners": ["谷广田", "吴志辉<PERSON>Z<PERSON>hui"], "category": ""}, {"id": 2443, "name": "fs-message-cloud", "remark": "--", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "", "mainOwner": "<PERSON>硕<PERSON><PERSON>", "owners": ["梁岩超Charles", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>"], "category": ""}, {"id": 302, "name": "fs-metadata-rest", "remark": "元数据rest服务", "department": "PaaS业务基础平台元数据组", "orgs": [], "level": "L0", "mainOwner": "钱凌锋irony", "owners": ["杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian"], "category": "前台服务"}, {"id": 378, "name": "fs-paas-mongo-sharding-transfer", "remark": "mongo类数据迁移工具", "department": "PaaS业务基础平台流程组", "orgs": [], "level": "L3", "mainOwner": "万松<PERSON>", "owners": ["万松<PERSON>"], "category": ""}, {"id": 2486, "name": "fs-qixin-file3", "remark": "企信file专属云独立部署，仅能单节点部署", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L2", "mainOwner": "梁岩超Charles", "owners": ["梁岩超Charles"], "category": "前台服务"}, {"id": 538, "name": "fs-task-manage-server", "remark": "定时提醒服务", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "", "mainOwner": "梁岩超Charles", "owners": ["尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>", "梁岩超Charles"], "category": ""}, {"id": 31, "name": "checkins-v2-history", "remark": "外勤迁移和刷库脚本服务", "department": "消费品业务技术外勤组", "orgs": ["快销农牧行业组"], "level": "L3", "mainOwner": "张世民", "owners": ["贺政忠HeZhengzhong", "何静媛hejy", "郭思卿", "张世民"], "category": ""}, {"id": 70, "name": "fs-appserver-pm-bizserver", "remark": "项目助手", "department": "消费品业务技术组", "orgs": [], "level": "L2", "mainOwner": "贺政忠HeZhengzhong", "owners": ["贺政忠HeZhengzhong", "何静媛hejy", "郭思卿", "<PERSON>海生", "张世民"], "category": ""}, {"id": 2556, "name": "fs-crm-reconciliation", "remark": "客户账户2.0对账系统", "department": "互联业务订货业务组", "orgs": [], "level": "L2", "mainOwner": "王凡", "owners": ["王凡", "袁杰<PERSON><PERSON>d"], "category": "前台服务"}, {"id": 238, "name": "fs-integral-provider", "remark": "积分系统", "department": "消费品业务技术销费组", "orgs": ["快销农牧行业组"], "level": "L2", "mainOwner": "李光田liguangtian", "owners": ["杨庆飞", "贺政忠HeZhengzhong", "薄川川Kele", "李光田liguangtian"], "category": "前台服务"}, {"id": 565, "name": "fs-warehouse-batch", "remark": "文件批量打包", "department": "平台运营架构组", "orgs": ["平台运营-平台架构部"], "level": "L2", "mainOwner": "安宜龙Andy", "owners": ["安宜龙Andy", "刘云松samuel"], "category": ""}, {"id": 2491, "name": "fs-open-feishu-all", "remark": "fs-open-feishu-all", "department": "互联业务集成平台组", "orgs": [], "level": "", "mainOwner": "陈宗鑫chenzongxin", "owners": ["陈宗鑫chenzongxin"], "category": ""}, {"id": 2524, "name": "fs-marketing-cloud-gray", "remark": "营销业务", "department": "深研营销业务部", "orgs": [], "level": "L2", "mainOwner": "郑辉Harry", "owners": ["郑辉Harry"], "category": "前台服务"}, {"id": 296, "name": "fs-metadata-data", "remark": "扫库", "department": "PaaS业务基础平台元数据组", "orgs": ["平台运营-PaaS"], "level": "L1", "mainOwner": "钱凌锋irony", "owners": ["杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian"], "category": "后台异步服务"}, {"id": 320, "name": "fs-open-app-center-all", "remark": "应用中心专属云发布", "department": "PaaS业务业务平台基础对象组", "orgs": [], "level": "L1", "mainOwner": "周伟荣", "owners": ["郑磊<PERSON><PERSON><PERSON>", "周伟荣"], "category": "前台服务"}, {"id": 323, "name": "fs-open-app-manage", "remark": "应用中心超级管理后台", "department": "PaaS业务业务平台基础对象组", "orgs": [], "level": "L2", "mainOwner": "周伟荣", "owners": ["李秋林<PERSON>", "冯永亮Frank", "丁成<PERSON><PERSON>", "郑磊<PERSON><PERSON><PERSON>", "周伟荣"], "category": "前台服务"}, {"id": 2485, "name": "fs-qixin-file2", "remark": "企信文件", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "", "mainOwner": "梁岩超Charles", "owners": ["尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>", "梁岩超Charles"], "category": ""}, {"id": 654, "name": "qywx-account-sync-provider", "remark": "企业微信帐号同步服务", "department": "互联业务集成平台组", "orgs": ["互联业务-产品合作组"], "level": "L1", "mainOwner": "冯院华Hardy", "owners": ["冯院华Hardy", "陈宗鑫chenzongxin"], "category": "后台异步服务"}, {"id": 146, "name": "fs-crm-fmcg", "remark": "fs-crm 快消企业专用环境", "department": "消费品业务技术组", "orgs": ["快销农牧行业组", "销售业务-自定义对象"], "level": "L3", "mainOwner": "杨庆飞", "owners": ["贺政忠HeZhengzhong", "何静媛hejy", "郭思卿", "张世民"], "category": ""}, {"id": 256, "name": "fs-keshun-connector", "remark": "科顺客开项目", "department": "制造行业客开组", "orgs": [], "level": "L2", "mainOwner": "李秋林<PERSON>", "owners": ["颜彩朵"], "category": ""}, {"id": 278, "name": "fs-marketing", "remark": "营销通", "department": "营销业务营销业务组", "orgs": [], "level": "L1", "mainOwner": "郑辉Harry", "owners": ["郑辉Harry", "罗勇Pate", "张树锋Halcyon", "周浮洋bruce", "张玉佳Mike"], "category": "前台服务"}, {"id": 360, "name": "fs-paas-batch-web", "remark": "数据导入导出删除工具", "department": "PaaS业务基础平台元数据组", "orgs": [], "level": "L2", "mainOwner": "杨华国YHG", "owners": ["杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian"], "category": "后台异步服务"}, {"id": 99, "name": "fs-bi-metadata-ant", "remark": "BI新元数据同步服务 负责人： 刘强，霍冬杰，翟付杰", "department": "数据业务技术BI组", "orgs": [], "level": "L2", "mainOwner": "罗江林luojianglin", "owners": ["翟付杰Jeffrey", "陈江飞chenjiangfei", "郑子阳Julian"], "category": ""}, {"id": 2413, "name": "fs-manufacturing-task", "remark": "制造行业异步任务服务", "department": "制造行业开发组1", "orgs": [], "level": "L1", "mainOwner": "郭明Zero", "owners": ["李秋林<PERSON>", "郭明Zero", "谢向达", "戴云", "韦建韩Barry", "颜彩朵", "杨希luke", "丁成<PERSON><PERSON>"], "category": "后台异步服务"}, {"id": 393, "name": "fs-paas-workflow-cloud", "remark": "专属云引擎服务", "department": "PaaS业务基础平台流程组", "orgs": [], "level": "L0", "mainOwner": "万松<PERSON>", "owners": ["郑子阳Julian", "崔永旭", "万松<PERSON>", "宁天伟Ethan", "梁楠liangnan", "刘畅CharonLucca"], "category": "合并部署服务"}, {"id": 303, "name": "fs-metadata-rest-cloud", "remark": "元数据rest服务", "department": "PaaS业务基础平台元数据组", "orgs": [], "level": "L0", "mainOwner": "钱凌锋irony", "owners": ["杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian"], "category": "合并部署服务"}, {"id": 389, "name": "fs-paas-sysmanage-compose", "remark": "翻译工作台专属云环境", "department": "PaaS业务基础平台元数据组", "orgs": ["平台运营-平台架构部", "翻译工作台发布"], "level": "L1", "mainOwner": "吕杰lvjie", "owners": ["杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian"], "category": "前台服务"}, {"id": 597, "name": "i18n-cgi", "remark": "国际化接口服务", "department": "平台运营架构组", "orgs": ["平台运营-PaaS", "PaaS-国际化"], "level": "L2", "mainOwner": "谷广田", "owners": ["谷广田", "杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian"], "category": ""}, {"id": 26, "name": "cctrl-center", "remark": "云控，相当于app和web版的配置中心，properties格式，可以是任何数据，一般用于配置一些开关项目，例如 是否打开或者打开某个功能，配置某个服务的地址等; 如果此服务异常，app无法获取配置", "department": "平台运营架构组", "orgs": [], "level": "L2", "mainOwner": "侯世鹏", "owners": ["侯世鹏"], "category": "前台服务"}, {"id": 40, "name": "ct-log-scanner", "remark": "数据迁移ctLog扫描", "department": "北研业务线", "orgs": ["平台运营-PaaS"], "level": "", "mainOwner": "谷广田", "owners": ["谷广田", "李磊Leo", "郑子阳Julian"], "category": ""}, {"id": 191, "name": "fs-enterprise-admin-provider", "remark": "企业互联-管理服务", "department": "互联业务互联平台组", "orgs": ["互联业务-互联平台组"], "level": "L3", "mainOwner": "曾文汶sunny", "owners": ["钟兴ZhongXing", "曾文汶sunny", "韦宁宁Jack<PERSON><PERSON>"], "category": ""}, {"id": 241, "name": "fs-invite-biz", "remark": "邀约接入层", "department": "PaaS业务业务平台开发平台组", "orgs": [], "level": "L2", "mainOwner": "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "owners": ["陈晓彬", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 2339, "name": "orion-web", "remark": "好丽友数据导入到CRM服务", "department": "消费品业务技术组", "orgs": ["快销农牧行业组", "平台运营-平台架构部"], "level": "L2", "mainOwner": "郭思卿", "owners": ["贺政忠HeZhengzhong", "郭思卿", "杨亚兴"], "category": ""}, {"id": 2492, "name": "fs-crm-pay", "remark": "新版支付平台", "department": "制造行业开发组1", "orgs": [], "level": "", "mainOwner": "郭明Zero", "owners": ["郭明Zero"], "category": ""}, {"id": 2573, "name": "fs-polling-provider", "remark": "轮询provider", "department": "平台运营架构组", "orgs": [], "level": "L2", "mainOwner": "谷广田", "owners": ["谷广田", "吴志辉<PERSON>Z<PERSON>hui"], "category": "前台服务"}, {"id": 2356, "name": "houshipeng-test", "remark": "侯世鹏个人测试", "department": "", "orgs": [], "level": "", "mainOwner": "", "owners": [], "category": ""}, {"id": 599, "name": "i18n-message", "remark": "多语服务统一消息通知服务", "department": "PaaS业务基础平台元数据组", "orgs": ["PaaS-国际化"], "level": "L2", "mainOwner": "梁梓闻liang<PERSON><PERSON>", "owners": ["杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian"], "category": "后台异步服务"}, {"id": 428, "name": "fs-plat-app-view-cgi", "remark": "H5接入层", "department": "PaaS业务业务平台开发平台组", "orgs": ["销售协同-平台业务部", "互联业务-互联平台组", "互联业务-产品合作组"], "level": "L0", "mainOwner": "丁龙飞<PERSON><PERSON>", "owners": ["丁龙飞<PERSON><PERSON>", "吴俊文Raymond"], "category": "前台服务"}, {"id": 440, "name": "fs-plat-netdisk-biz", "remark": "网盘服务", "department": "平台运营架构组", "orgs": ["平台运营-平台架构部"], "level": "L2", "mainOwner": "刘云松samuel", "owners": ["安宜龙Andy", "刘云松samuel"], "category": ""}, {"id": 2474, "name": "fs-apibus-external", "remark": "apibus 外部无身份路由服务", "department": "PaaS业务业务平台基础对象组", "orgs": [], "level": "L2", "mainOwner": "郑磊<PERSON><PERSON><PERSON>", "owners": ["郑磊<PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 62, "name": "fs-appserver-checkins-v2-gray", "remark": "外勤签到服务-灰度环境", "department": "消费品业务技术外勤组", "orgs": ["快销农牧行业组"], "level": "L2", "mainOwner": "贺政忠HeZhengzhong", "owners": ["贺政忠HeZhengzhong", "何静媛hejy", "郭思卿", "张世民"], "category": ""}, {"id": 177, "name": "fs-dataplatform-metrics-server", "remark": "负责采集大数据平台核心指标入到kibana或者神策中", "department": "数据业务技术平台组", "orgs": [], "level": "L3", "mainOwner": "纪二飞Roy", "owners": [], "category": ""}, {"id": 2450, "name": "fs-fmcg-sales-cgi-gray", "remark": "蒙牛云测试环境", "department": "消费品业务技术创新组", "orgs": ["快销农牧行业组"], "level": "L2", "mainOwner": "杨亚兴", "owners": ["杨亚兴"], "category": ""}, {"id": 554, "name": "fs-user-extension-biz", "remark": "App自定义服务-业务接口", "department": "PaaS业务业务平台基础对象组", "orgs": [], "level": "L1", "mainOwner": "周伟荣", "owners": ["郑磊<PERSON><PERSON><PERSON>", "周伟荣"], "category": "前台服务"}, {"id": 41, "name": "data-auth-service", "remark": "数据权限详情鉴权、接口服务", "department": "PaaS业务基础平台元数据组", "orgs": [], "level": "L0", "mainOwner": "杨华国YHG", "owners": ["杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian", "李锐colin"], "category": "前台服务"}, {"id": 2415, "name": "fs-enterprise-relation-biz-global", "remark": "互联平台全局服务", "department": "互联业务互联平台组", "orgs": ["互联业务-互联平台组"], "level": "L2", "mainOwner": "曾文汶sunny", "owners": ["曾文汶sunny", "钟兴ZhongXing", "韦宁宁Jack<PERSON><PERSON>"], "category": ""}, {"id": 2553, "name": "fs-flow-orch", "remark": "流程OneFlow独立使用的fs-flow服务", "department": "PaaS业务基础平台流程组", "orgs": [], "level": "L1", "mainOwner": "宁天伟Ethan", "owners": [], "category": "前台服务"}, {"id": 347, "name": "fs-open-webhook-message-send", "remark": "云之家消息集成服务", "department": "互联业务集成平台组", "orgs": ["互联业务-开平对接组", "互联业务-产品合作组"], "level": "L2", "mainOwner": "柯南颖", "owners": ["柯南颖"], "category": "后台异步服务"}, {"id": 353, "name": "fs-paas-app-task", "remark": "业务平台的任务处理服务", "department": "PaaS业务业务平台基础对象组", "orgs": [], "level": "L2", "mainOwner": "赵琚", "owners": ["周伟荣", "赵琚", "郑磊<PERSON><PERSON><PERSON>", "王毅Wang<PERSON>i", "冯津<PERSON><PERSON><PERSON>"], "category": "后台异步服务"}, {"id": 21, "name": "attachment-search-datamgr-index", "remark": "附件搜索实时数据新增管理", "department": "深研营销业务部", "orgs": [], "level": "", "mainOwner": "郑辉Harry", "owners": ["郑辉Harry"], "category": ""}, {"id": 2496, "name": "fs-crm-fmcg-service-urgent", "remark": "蒙牛沙盒 urgent 环境", "department": "消费品业务技术销费组", "orgs": ["快销农牧行业组"], "level": "L2", "mainOwner": "杨庆飞", "owners": ["杨庆飞", "林明杰", "<PERSON>京<PERSON>", "吴永鑫"], "category": ""}, {"id": 290, "name": "fs-message-kingdee", "remark": "金蝶云之家消息服务", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L2", "mainOwner": "<PERSON>硕<PERSON><PERSON>", "owners": ["尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>", "梁岩超Charles"], "category": "后台异步服务"}, {"id": 305, "name": "fs-myfavourites-biz", "remark": "收藏", "department": "PaaS业务业务平台协同业务组", "orgs": ["销售协同-平台业务部"], "level": "L2", "mainOwner": "王宙Joseph", "owners": ["尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 649, "name": "pg-scanner-ui", "remark": "扫描paas数据库的变更记录控制台", "department": "PaaS业务基础平台部", "orgs": ["平台运营-PaaS"], "level": "L2", "mainOwner": "谷广田", "owners": ["谷广田", "李锐colin", "李磊Leo", "郑子阳Julian"], "category": ""}, {"id": 262, "name": "fs-kiscloud-task", "remark": "kis云对接服务", "department": "互联业务集成平台组", "orgs": ["互联业务-开平对接组", "互联业务-产品合作组"], "level": "L2", "mainOwner": "冯院华Hardy", "owners": ["冯院华Hardy", "柯南颖"], "category": "后台异步服务"}, {"id": 274, "name": "fs-mankeep-miniapp", "remark": "客脉项目接入服务", "department": "营销业务营销业务组", "orgs": ["互联业务-客脉业务组"], "level": "L2", "mainOwner": "郑辉Harry", "owners": ["郑辉Harry", "张胜斌zhangshengbin"], "category": ""}, {"id": 532, "name": "fs-stone-transfer", "remark": "文件迁移工具", "department": "平台运营架构组", "orgs": [], "level": "L3", "mainOwner": "安宜龙Andy", "owners": ["安宜龙Andy"], "category": ""}, {"id": 558, "name": "fs-video", "remark": "培训助手视频播放", "department": "深研营销业务部", "orgs": [], "level": "", "mainOwner": "张玉佳Mike", "owners": ["郑辉Harry", "林鸣Ming", "冯永亮Frank", "钟兴ZhongXing"], "category": ""}, {"id": 354, "name": "fs-paas-app-udobj", "remark": "paas自定义对象接口", "department": "PaaS业务业务平台基础对象组", "orgs": ["销售业务-自定义对象"], "level": "L0", "mainOwner": "郑磊<PERSON><PERSON><PERSON>", "owners": ["郑磊<PERSON><PERSON><PERSON>", "郑子阳Julian", "李磊Leo", "赵琚", "周伟荣", "王毅Wang<PERSON>i", "冯津<PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 377, "name": "fs-paas-metadata-dataloader", "remark": "paas平台统一导入服务", "department": "PaaS业务业务平台基础对象组", "orgs": ["销售业务-自定义对象"], "level": "L2", "mainOwner": "赵琚", "owners": ["郑磊<PERSON><PERSON><PERSON>", "赵琚", "王毅Wang<PERSON>i", "周伟荣", "冯津<PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 22, "name": "attachment-search-datamgr-update", "remark": "主站附件搜索", "department": "深研营销业务部", "orgs": [], "level": "", "mainOwner": "郑辉Harry", "owners": ["郑辉Harry"], "category": ""}, {"id": 30, "name": "checkins-office-v2-task", "remark": "新考勤的定时任务", "department": "消费品业务技术外勤组", "orgs": ["快销农牧行业组"], "level": "L2", "mainOwner": "张世民", "owners": ["贺政忠HeZhengzhong", "郭思卿", "张世民"], "category": ""}, {"id": 2574, "name": "config-service-v2", "remark": "配置中心v2", "department": "平台运营架构组", "orgs": [], "level": "L1", "mainOwner": "侯世鹏", "owners": ["李锐colin", "侯世鹏"], "category": "后台异步服务"}, {"id": 60, "name": "fs-appserver-battlereport-assistant-web", "remark": "战报助手服务", "department": "消费品业务技术组", "orgs": ["快销农牧行业组"], "level": "L2", "mainOwner": "贺政忠HeZhengzhong", "owners": ["贺政忠HeZhengzhong", "何静媛hejy", "张华", "安丰胜Richard", "郭思卿", "张世民"], "category": ""}, {"id": 634, "name": "open-api-admin-web", "remark": "openapi-管理后台", "department": "互联业务互联平台组", "orgs": [], "level": "L3", "mainOwner": "刘诗林liushilin", "owners": ["刘诗林liushilin", "张诚fine", "曾令文Evan", "钟兴ZhongXing"], "category": ""}, {"id": 116, "name": "fs-bi-yearend-activities-server", "remark": "年终运营活动", "department": "数据业务技术平台组", "orgs": [], "level": "", "mainOwner": "纪二飞Roy", "owners": ["纪二飞Roy", "吴丕华"], "category": ""}, {"id": 169, "name": "fs-customer-component-provider", "remark": "自定义组件", "department": "PaaS业务业务平台开发平台组", "orgs": [], "level": "L2", "mainOwner": "吴俊文Raymond", "owners": ["张晓峰zxf", "吴俊文Raymond"], "category": "前台服务"}, {"id": 259, "name": "fs-kis-connector-web", "remark": "金蝶对接应用接入层", "department": "平台运营运维组", "orgs": ["销售协同-平台业务部", "互联业务-开平对接组", "互联业务-产品合作组", "互联业务-客脉业务组"], "level": "L2", "mainOwner": "冯院华Hardy", "owners": ["冯院华Hardy"], "category": ""}, {"id": 593, "name": "fs-zhzq-connector", "remark": "4552396_陈祥飞_中华制漆定制化报表项目", "department": "集成开发组", "orgs": ["互联业务-开平对接组", "互联业务-产品合作组"], "level": "", "mainOwner": "凌钦羽", "owners": [], "category": ""}, {"id": 2517, "name": "fs-egress-api-notify-push", "remark": "消息推送后端服务", "department": "平台运营架构组", "orgs": [], "level": "L2", "mainOwner": "刘全胜liuquansheng", "owners": ["刘全胜liuquansheng", "吴志辉<PERSON>Z<PERSON>hui"], "category": "后台异步服务"}, {"id": 249, "name": "fs-k3cloud-task", "remark": "老K3对接插件任务模块", "department": "互联业务集成平台组", "orgs": ["互联业务-开平对接组", "互联业务-产品合作组"], "level": "", "mainOwner": "冯院华Hardy", "owners": ["谢嘉裕Ken", "冯永亮Frank", "冯院华Hardy", "冯永亮Frank"], "category": ""}, {"id": 372, "name": "fs-paas-job-schedule", "remark": "paas计算、达芬奇任务调度", "department": "PaaS业务业务平台基础对象组", "orgs": [], "level": "L0", "mainOwner": "李远", "owners": ["王毅Wang<PERSON>i", "李远", "郑磊<PERSON><PERSON><PERSON>", "周伟荣"], "category": "后台异步服务"}, {"id": 73, "name": "fs-appserver-schedule-provider", "remark": "日历数据获取", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L2", "mainOwner": "<PERSON>硕<PERSON><PERSON>", "owners": ["尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>", "张文超"], "category": "前台服务"}, {"id": 173, "name": "fs-dataplatform-feed2gp-consumer", "remark": "4637213_销售记录数据同步入greenplum_负责人：纪二飞,彭帅", "department": "数据业务技术平台组", "orgs": ["销售业务-BI"], "level": "L2", "mainOwner": "纪二飞Roy", "owners": [], "category": ""}, {"id": 344, "name": "fs-open-webhook-accountbind", "remark": "金蝶账号绑定服务", "department": "制造行业客开组", "orgs": ["互联业务-开平对接组", "互联业务-产品合作组"], "level": "L2", "mainOwner": "冯院华Hardy", "owners": ["柯南颖", "冯永亮Frank", "冯院华Hardy", "冯永亮Frank"], "category": ""}, {"id": 356, "name": "fs-paas-app-udobj-rest4flow", "remark": "udobj for 流程", "department": "PaaS业务业务平台基础对象组", "orgs": ["销售业务-自定义对象"], "level": "L1", "mainOwner": "冯津<PERSON><PERSON><PERSON>", "owners": ["郑磊<PERSON><PERSON><PERSON>", "周伟荣", "赵琚", "万松<PERSON>", "王毅Wang<PERSON>i", "冯津<PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 425, "name": "fs-pay-web", "remark": "支付服务", "department": "制造行业开发组1", "orgs": [], "level": "L2", "mainOwner": "郭明Zero", "owners": ["谢向达", "丁成<PERSON><PERSON>", "郭明Zero"], "category": ""}, {"id": 495, "name": "fs-refresh-es-data", "remark": "元数据刷ES搜索", "department": "PaaS业务基础平台元数据组", "orgs": ["平台运营-平台架构部", "平台运营-PaaS"], "level": "L0", "mainOwner": "李晨Jason", "owners": ["杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian"], "category": "后台异步服务"}, {"id": 2416, "name": "fs-enterprise-relation-biz-login", "remark": "互联平台登录服务", "department": "互联业务互联平台组", "orgs": ["互联业务-互联平台组"], "level": "L1", "mainOwner": "曾文汶sunny", "owners": ["曾文汶sunny", "钟兴ZhongXing", "韦宁宁Jack<PERSON><PERSON>"], "category": "前台服务"}, {"id": 197, "name": "fs-erp-sync-data-task", "remark": "ERP数据同步定时任务服务", "department": "互联业务集成平台组", "orgs": [], "level": "L1", "mainOwner": "冯院华Hardy", "owners": ["冯院华Hardy", "谢嘉裕Ken", "郭远东", "柯南颖", "冯永亮Frank", "陈晓彬"], "category": "后台异步服务"}, {"id": 346, "name": "fs-open-webhook-eventhandler", "remark": "Webhook第三方回调入口服务", "department": "制造行业客开组", "orgs": ["互联业务-开平对接组", "互联业务-产品合作组"], "level": "L2", "mainOwner": "冯院华Hardy", "owners": ["柯南颖", "冯永亮Frank", "冯院华Hardy"], "category": ""}, {"id": 386, "name": "fs-paas-rule", "remark": "规则引擎服务", "department": "PaaS业务基础平台元数据组", "orgs": [], "level": "L0", "mainOwner": "吕杰lvjie", "owners": ["杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian", "王栋轩"], "category": "前台服务"}, {"id": 625, "name": "jdy-connector-service", "remark": "京东云对接服务", "department": "SFA业务售前组", "orgs": [], "level": "L3", "mainOwner": "陈金典Kimd", "owners": ["刘军超"], "category": "后台异步服务"}, {"id": 84, "name": "fs-bi-custom-statistic-dim-online", "remark": "自定义统计图dim实时计算", "department": "数据业务技术平台组", "orgs": ["销售业务-BI", "平台运营-平台架构部"], "level": "L2", "mainOwner": "纪二飞Roy", "owners": ["纪二飞Roy", "赵孟华HuaZai", "郑子阳Julian", "肖扬Jack"], "category": ""}, {"id": 2430, "name": "fs-devops-console", "remark": "devops的管理后台", "department": "平台运营架构组", "orgs": [], "level": "L3", "mainOwner": "吴志辉<PERSON>Z<PERSON>hui", "owners": ["李锐colin", "吴志辉<PERSON>Z<PERSON>hui"], "category": "合并部署服务"}, {"id": 497, "name": "fs-register-provider", "remark": "新版注册服务", "department": "PaaS业务业务平台开发平台组", "orgs": ["销售协同-平台业务部"], "level": "L2", "mainOwner": "吴俊文Raymond", "owners": ["吴俊文Raymond", "张勇ZhangYong"], "category": "前台服务"}, {"id": 535, "name": "fs-sync-data-all", "remark": "数据同步", "department": "互联业务互联平台组", "orgs": ["互联业务-互联平台组"], "level": "L1", "mainOwner": "刘诗林liushilin", "owners": ["刘诗林liushilin", "张诚fine", "曾令文Evan", "钟兴ZhongXing"], "category": "后台异步服务"}, {"id": 55, "name": "fs-ai-detector-provider", "remark": "图像识别服务", "department": "消费品业务技术销费组", "orgs": ["快销农牧行业组"], "level": "L1", "mainOwner": "林明杰", "owners": ["杨庆飞", "贺政忠HeZhengzhong", "杨亚兴", "林明杰", "吴永鑫"], "category": "前台服务"}, {"id": 391, "name": "fs-paas-web", "remark": "describe回源访问，数据查询", "department": "PaaS业务基础平台元数据组", "orgs": [], "level": "L1", "mainOwner": "杨华国YHG", "owners": ["杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian"], "category": "后台异步服务"}, {"id": 442, "name": "fs-plat-org-adapter-provider", "remark": "内外部通讯录", "department": "PaaS业务业务平台协同业务组", "orgs": ["销售协同-平台业务部"], "level": "L1", "mainOwner": "刘文绪dio", "owners": ["刘文绪dio", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 660, "name": "tenant-sandbox", "remark": "沙箱拷贝服务", "department": "PaaS业务基础平台元数据组", "orgs": ["平台运营-PaaS", "销售业务-自定义对象"], "level": "L1", "mainOwner": "钱凌锋irony", "owners": ["杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian"], "category": "后台异步服务"}, {"id": 2397, "name": "fs-crm-fmcg-wq", "remark": "fmcg 外勤crm项目", "department": "消费品业务技术外勤组", "orgs": [], "level": "L1", "mainOwner": "张世民", "owners": ["贺政忠HeZhengzhong", "何静媛hejy", "郭思卿", "张世民", "张晓攀", "张常清zhangchangqing"], "category": "前台服务"}, {"id": 150, "name": "fs-crm-integral-task", "remark": "CRM行为积分任务服务", "department": "SFA业务技术售中技术组", "orgs": [], "level": "L2", "mainOwner": "赵鹏欣Paul", "owners": ["赵鹏欣Paul", "任林波Ren<PERSON><PERSON>bo", "吉明哲daemon"], "category": "后台异步服务"}, {"id": 530, "name": "fs-stone-proxy", "remark": "企业文件（stone）系统路由网关", "department": "平台运营架构组", "orgs": [], "level": "L0", "mainOwner": "安宜龙Andy", "owners": ["安宜龙Andy"], "category": "前台服务"}, {"id": 562, "name": "fs-wangsen-pay", "remark": "王森客开项目", "department": "SFA业务技术售中技术组", "orgs": [], "level": "", "mainOwner": "宋长乾", "owners": ["宋长乾"], "category": ""}, {"id": 529, "name": "fs-stone-metaserver", "remark": "企业文件（stone）元数据服务", "department": "平台运营架构组", "orgs": [], "level": "L0", "mainOwner": "安宜龙Andy", "owners": ["安宜龙Andy"], "category": "前台服务"}, {"id": 586, "name": "fs-workflow", "remark": "工作流服务", "department": "PaaS业务基础平台流程组", "orgs": [], "level": "L1", "mainOwner": "刘畅CharonLucca", "owners": ["崔永旭", "万松<PERSON>", "宁天伟Ethan", "梁楠liangnan", "刘畅CharonLucca"], "category": "前台服务"}, {"id": 18, "name": "async-job-import", "remark": "任务中心-异步导入导出", "department": "平台运营架构组", "orgs": ["平台运营-PaaS", "平台运营-平台架构部"], "level": "L1", "mainOwner": "谷广田", "owners": ["谷广田", "吴志辉<PERSON>Z<PERSON>hui"], "category": "后台异步服务"}, {"id": 2583, "name": "fs-crm-sfa-agent", "remark": "SFA agent项目", "department": "SFA业务技术售中业财组", "orgs": [], "level": "L2", "mainOwner": "任林波Ren<PERSON><PERSON>bo", "owners": [], "category": "后台异步服务"}, {"id": 2304, "name": "fs-metadata-provider", "remark": "元数据的rest接口服务", "department": "PaaS业务基础平台元数据组", "orgs": ["平台运营-PaaS"], "level": "L1", "mainOwner": "钱凌锋irony", "owners": ["杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian"], "category": "前台服务"}, {"id": 318, "name": "fs-online-consult-web", "remark": "在线客服web层", "department": "制造行业开发组1", "orgs": [], "level": "L1", "mainOwner": "戴云", "owners": ["戴云", "郭明Zero"], "category": "前台服务"}, {"id": 48, "name": "erpdss-u8-midtable", "remark": "ERP数据同步服务-u8专用组件", "department": "互联业务集成平台组", "orgs": ["互联业务-开平对接组"], "level": "L1", "mainOwner": "冯院华Hardy", "owners": ["冯院华Hardy", "谢嘉裕Ken", "郭远东", "柯南颖", "冯永亮Frank", "陈晓彬", "刘显诗"], "category": ""}, {"id": 2472, "name": "fs-file-server", "remark": "文件系统统一接入", "department": "平台运营架构组", "orgs": [], "level": "L0", "mainOwner": "安宜龙Andy", "owners": ["安宜龙Andy"], "category": "前台服务"}, {"id": 226, "name": "fs-fsc-cgi", "remark": "文件服务网关", "department": "平台运营架构组", "orgs": [], "level": "L0", "mainOwner": "安宜龙Andy", "owners": ["安宜龙Andy", "吴志辉<PERSON>Z<PERSON>hui"], "category": "前台服务"}, {"id": 2469, "name": "hisense-custom-provider", "remark": "海信客开项目", "department": "北方战区大客户经营技术顾问组", "orgs": [], "level": "", "mainOwner": "陈仁峰max", "owners": ["陈仁峰max"], "category": ""}, {"id": 179, "name": "fs-datapt-employee-profiler", "remark": "线索推荐服务，公司舆情服务，公司工商信息详情页", "department": "数据业务技术平台组", "orgs": [], "level": "L2", "mainOwner": "纪二飞Roy", "owners": ["纪二飞Roy", "赵孟华HuaZai", "赵正豪zhenghao"], "category": ""}, {"id": 188, "name": "fs-dns-parser", "remark": "终端自救拉取DNS", "department": "北研业务线", "orgs": [], "level": "", "mainOwner": "李锐colin", "owners": ["李锐colin", "吴志辉<PERSON>Z<PERSON>hui"], "category": ""}, {"id": 2407, "name": "fs-erp-sync-data-monitor", "remark": "集成平台监控模块 ", "department": "互联业务集成平台组", "orgs": [], "level": "L2", "mainOwner": "冯院华Hardy", "owners": ["冯院华Hardy"], "category": "后台异步服务"}, {"id": 2344, "name": "fs-crm-call-center-gray", "remark": "呼叫中心灰度环境", "department": "制造行业开发组1", "orgs": [], "level": "", "mainOwner": "戴云", "owners": [], "category": ""}, {"id": 2513, "name": "fs-crm-fmcg-reward-web", "remark": "快消任务系统", "department": "消费品业务技术销费组", "orgs": ["快销农牧行业组"], "level": "L2", "mainOwner": "李光田liguangtian", "owners": ["杨亚兴", "李光田liguangtian"], "category": ""}, {"id": 2531, "name": "fs-idp-helper", "remark": "", "department": "平台运营架构组", "orgs": [], "level": "L2", "mainOwner": "刘全胜liuquansheng", "owners": ["刘全胜liuquansheng"], "category": "前台服务"}, {"id": 403, "name": "fs-pay-core", "remark": "支付服务", "department": "制造行业开发组1", "orgs": [], "level": "L2", "mainOwner": "郭明Zero", "owners": ["谢向达", "丁成<PERSON><PERSON>", "郭明Zero"], "category": ""}, {"id": 304, "name": "fs-mobile-location", "remark": "手机归属地查询服务 负责人： 吴志辉", "department": "", "orgs": [], "level": "", "mainOwner": "", "owners": [], "category": ""}, {"id": 2400, "name": "fs-mq-sort", "remark": "客户账户-校验规则", "department": "互联业务订货业务组", "orgs": [], "level": "L2", "mainOwner": "王凡", "owners": ["王凡", "袁杰<PERSON><PERSON>d", "陈柱深"], "category": "后台异步服务"}, {"id": 348, "name": "fs-organization-adapter", "remark": "通讯录adapter服务", "department": "PaaS业务业务平台协同业务组", "orgs": ["销售协同-平台业务部"], "level": "L1", "mainOwner": "刘文绪dio", "owners": ["刘文绪dio", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 387, "name": "fs-paas-score", "remark": "打分器服务", "department": "PaaS业务业务平台基础对象组", "orgs": ["销售业务-自定义对象"], "level": "L2", "mainOwner": "赵琚", "owners": ["王毅Wang<PERSON>i", "郑磊<PERSON><PERSON><PERSON>", "周伟荣", "赵琚", "冯津<PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 2335, "name": "dev-online-tool", "remark": "纷享开发者在线平台", "department": "平台运营架构组", "orgs": [], "level": "L3", "mainOwner": "吴志辉<PERSON>Z<PERSON>hui", "owners": [], "category": ""}, {"id": 69, "name": "fs-appserver-payment-web", "remark": "打赏系统", "department": "消费品业务技术组", "orgs": ["快销农牧行业组"], "level": "L3", "mainOwner": "贺政忠HeZhengzhong", "owners": ["贺政忠HeZhengzhong", "何静媛hejy"], "category": ""}, {"id": 168, "name": "fs-custom-provider", "remark": "客开项目服务", "department": "高服行业ABU组", "orgs": ["销售业务-SFA"], "level": "L3", "mainOwner": "陈金典Kimd", "owners": ["陈金典Kimd", "宋长乾"], "category": ""}, {"id": 2408, "name": "fs-erp-sync-data-file", "remark": "集成平台-文件模块", "department": "互联业务集成平台组", "orgs": [], "level": "L1", "mainOwner": "冯院华Hardy", "owners": ["冯院华Hardy"], "category": "后台异步服务"}, {"id": 412, "name": "fs-pay-support", "remark": "支付服务", "department": "深研制造行业部", "orgs": [], "level": "L2", "mainOwner": "郭明Zero", "owners": ["谢向达", "丁成<PERSON><PERSON>", "郭明Zero"], "category": ""}, {"id": 2346, "name": "fs-erp-sync-data-web", "remark": "ERP数据同步web服务", "department": "互联业务集成平台组", "orgs": ["互联业务-开平对接组", "互联业务-产品合作组"], "level": "L1", "mainOwner": "冯院华Hardy", "owners": ["冯院华Hardy", "谢嘉裕Ken", "柯南颖", "冯永亮Frank", "郭远东", "陈晓彬"], "category": "前台服务"}, {"id": 2367, "name": "fs-eservice-mq-listener", "remark": "制造行业组-服务通", "department": "制造行业开发组1", "orgs": [], "level": "L1", "mainOwner": "郭明Zero", "owners": ["郭明Zero", "丁成<PERSON><PERSON>", "戴云", "谢向达"], "category": "后台异步服务"}, {"id": 2557, "name": "fs-file-datax-stats", "remark": "文件用量统计服务", "department": "平台运营架构组", "orgs": [], "level": "L2", "mainOwner": "安宜龙Andy", "owners": ["安宜龙Andy", "刘云松samuel"], "category": "后台异步服务"}, {"id": 2383, "name": "fs-paas-function-service-background-provider", "remark": "", "department": "PaaS业务业务平台开发平台组", "orgs": [], "level": "L1", "mainOwner": "斯作益seth", "owners": ["吴俊文Raymond", "陈晓彬", "斯作益seth"], "category": "后台异步服务"}, {"id": 93, "name": "fs-bi-goal-web", "remark": "BI目标", "department": "数据业务技术BI组", "orgs": ["销售业务-BI"], "level": "L1", "mainOwner": "陈江飞chenjiangfei", "owners": ["翟付杰Jeffrey", "纪二飞Roy"], "category": "前台服务"}, {"id": 2535, "name": "fs-file-process", "remark": "AI多模态文件内容解析", "department": "平台运营架构组", "orgs": [], "level": "L2", "mainOwner": "安宜龙Andy", "owners": ["安宜龙Andy"], "category": "后台异步服务"}, {"id": 335, "name": "fs-open-material-web", "remark": "服务号-素材中心web入口", "department": "制造行业开发组2", "orgs": [], "level": "L2", "mainOwner": "李秋林<PERSON>", "owners": ["李秋林<PERSON>"], "category": "前台服务"}, {"id": 381, "name": "fs-paas-recycle", "remark": "paas资源回收", "department": "PaaS业务基础平台元数据组", "orgs": ["平台运营-PaaS"], "level": "L2", "mainOwner": "钱凌锋irony", "owners": ["杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian"], "category": "后台异步服务"}, {"id": 2534, "name": "fs-international-connector", "remark": "国际化连接器", "department": "制造行业开发组1", "orgs": [], "level": "L2", "mainOwner": "杨希luke", "owners": ["丁成<PERSON><PERSON>", "杨希luke"], "category": "后台异步服务"}, {"id": 437, "name": "fs-plat-feeds-cloud", "remark": "Feeds核心服务", "department": "PaaS业务业务平台协同业务组", "orgs": ["销售协同-平台业务部"], "level": "L2", "mainOwner": "张文超", "owners": ["张文超", "刘文绪dio", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 635, "name": "open-api-gateway-web", "remark": "openapi-网关服务-灰度", "department": "互联业务互联平台组", "orgs": [], "level": "L1", "mainOwner": "刘诗林liushilin", "owners": ["刘诗林liushilin", "张诚fine", "曾令文Evan", "钟兴ZhongXing"], "category": "前台服务"}, {"id": 58, "name": "fs-apibus-paas", "remark": "内部PaaS业务调用网关，承载获取对象描述、查询数据等非CRM业务接口调用", "department": "PaaS业务业务平台部", "orgs": ["销售业务-自定义对象", "平台运营-PaaS"], "level": "L0", "mainOwner": "郑磊<PERSON><PERSON><PERSON>", "owners": ["王毅Wang<PERSON>i", "郑磊<PERSON><PERSON><PERSON>", "赵琚"], "category": "前台服务"}, {"id": 2451, "name": "fs-bi-warehouse", "remark": "BICH数仓计算服务", "department": "数据业务技术平台组", "orgs": [], "level": "L1", "mainOwner": "纪二飞Roy", "owners": ["纪二飞Roy", "赵正豪zhenghao", "肖扬Jack", "赵孟华HuaZai", "郑子阳Julian"], "category": "后台异步服务"}, {"id": 2453, "name": "fs-crm-manufacturing-cloud-gray", "remark": "制造行业组专属云灰度环境", "department": "制造行业开发组2", "orgs": [], "level": "", "mainOwner": "李秋林<PERSON>", "owners": ["丁成<PERSON><PERSON>", "郭明Zero", "谢向达", "戴云", "李秋林<PERSON>", "颜彩朵", "韦建韩Barry", "杨希luke"], "category": ""}, {"id": 2414, "name": "fs-erp-sync-demoerp", "remark": "集成平台的erpdemo环境", "department": "互联业务集成平台组", "orgs": [], "level": "L3", "mainOwner": "冯院华Hardy", "owners": ["谢嘉裕Ken", "柯南颖", "冯院华Hardy", "郭远东", "陈晓彬", "吴贝贝"], "category": ""}, {"id": 199, "name": "fs-eservice", "remark": "服务通", "department": "制造行业开发组1", "orgs": [], "level": "L1", "mainOwner": "郭明Zero", "owners": ["丁成<PERSON><PERSON>", "郭明Zero", "谢向达", "丁成<PERSON><PERSON>"], "category": ""}, {"id": 2541, "name": "fs-flow-session-task", "remark": "研发问题排查服务号 task 模块", "department": "PaaS业务基础平台流程组", "orgs": [], "level": "L2", "mainOwner": "万松<PERSON>", "owners": ["万松<PERSON>"], "category": "前台服务"}, {"id": 294, "name": "fs-message-server-wrapper", "remark": "消息平台专属云合并服务", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L2", "mainOwner": "<PERSON>硕<PERSON><PERSON>", "owners": ["梁岩超Charles", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 478, "name": "fs-qixin-objgroup-manage-provider", "remark": "企信对象群管理", "department": "PaaS业务业务平台协同业务组", "orgs": ["销售协同-企信"], "level": "L2", "mainOwner": "梁岩超Charles", "owners": ["尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>", "梁岩超Charles"], "category": "前台服务"}, {"id": 114, "name": "fs-bi-udf-report", "remark": "CRM-BI-新报表服务-灰度服务", "department": "数据业务技术BI组", "orgs": [], "level": "L1", "mainOwner": "齐庆阳qiqingyang", "owners": ["翟付杰Jeffrey", "陈江飞chenjiangfei", "郑子阳Julian", "齐庆阳qiqingyang"], "category": "前台服务"}, {"id": 143, "name": "fs-contacts-search-provider", "remark": "通讯录搜索", "department": "PaaS业务业务平台开发平台组", "orgs": ["销售协同-平台业务部"], "level": "", "mainOwner": "吴俊文Raymond", "owners": ["王毅Wang<PERSON>i", "吴俊文Raymond"], "category": ""}, {"id": 2463, "name": "fs-crm-all", "remark": "基于Appframework开发的CRM服务集合", "department": "PaaS业务业务平台基础对象组", "orgs": [], "level": "L0", "mainOwner": "周伟荣", "owners": ["周伟荣"], "category": "前台服务"}, {"id": 2530, "name": "fs-crm-meeting", "remark": "activity对接腾讯会议", "department": "SFA业务高服行业技术组", "orgs": [], "level": "L2", "mainOwner": "吉明哲daemon", "owners": [], "category": ""}, {"id": 479, "name": "fs-qixin-plugin", "remark": "企信plugin", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L2", "mainOwner": "梁岩超Charles", "owners": ["尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>", "梁岩超Charles"], "category": ""}, {"id": 29, "name": "checkins-office-v2-server", "remark": "新考勤服务", "department": "消费品业务技术外勤组", "orgs": ["快销农牧行业组"], "level": "L1", "mainOwner": "张世民", "owners": ["贺政忠HeZhengzhong", "张华", "郭思卿", "张世民", "何静媛hejy"], "category": "前台服务"}, {"id": 445, "name": "fs-plat-polling-cloud", "remark": "轮询服务", "department": "平台运营架构组", "orgs": [], "level": "L2", "mainOwner": "谷广田", "owners": ["尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "刘文绪dio"], "category": "后台异步服务"}, {"id": 2354, "name": "hospital-spider", "remark": "医疗主数据", "department": "互联业务互联平台组", "orgs": [], "level": "L2", "mainOwner": "刘诗林liushilin", "owners": ["刘诗林liushilin", "张诚fine", "钟兴ZhongXing"], "category": ""}, {"id": 627, "name": "kafka2hdfs", "remark": "kafka2hdfs", "department": "数据业务技术平台组", "orgs": [], "level": "L2", "mainOwner": "纪二飞Roy", "owners": ["赵孟华HuaZai", "党兰良danglanliang"], "category": ""}, {"id": 231, "name": "fs-hj", "remark": "汇聚", "department": "SFA业务售前组", "orgs": [], "level": "L3", "mainOwner": "赵鹏欣Paul", "owners": ["赵鹏欣Paul"], "category": ""}, {"id": 396, "name": "fs-pay-admin", "remark": "支付管理系统", "department": "制造行业开发组1", "orgs": [], "level": "", "mainOwner": "郭明Zero", "owners": ["丁成<PERSON><PERSON>", "郭明Zero", "谢向达", "丁成<PERSON><PERSON>"], "category": ""}, {"id": 457, "name": "fs-plat-social-cloud", "remark": "社交业务对象服务", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L2", "mainOwner": "刘文绪dio", "owners": ["张文超", "刘文绪dio", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 217, "name": "fs-flow", "remark": "流程共用模块", "department": "PaaS业务基础平台流程组", "orgs": ["平台运营-PaaS"], "level": "L1", "mainOwner": "宁天伟Ethan", "owners": ["崔永旭", "万松<PERSON>", "宁天伟Ethan", "梁楠liangnan", "刘畅CharonLucca"], "category": "前台服务"}, {"id": 315, "name": "fs-online-consult-fcp", "remark": "在线客服fcp接口", "department": "制造行业开发组1", "orgs": ["互联业务-产品合作组", "互联业务-服务业务组"], "level": "L2", "mainOwner": "戴云", "owners": ["戴云"], "category": ""}, {"id": 603, "name": "i18n-setting", "remark": "翻译工作台", "department": "PaaS业务基础平台元数据组", "orgs": ["平台运营-平台架构部", "翻译工作台发布", "PaaS-国际化"], "level": "L2", "mainOwner": "吕杰lvjie", "owners": ["杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian"], "category": "前台服务"}, {"id": 642, "name": "paas-db-operator", "remark": "初始化schema以及schema隔离企业动态加表、加字段", "department": "PaaS业务基础平台元数据组", "orgs": ["平台运营-PaaS"], "level": "L1", "mainOwner": "钱凌锋irony", "owners": ["杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian"], "category": "后台异步服务"}, {"id": 2580, "name": "egress-proxy-cross", "remark": "", "department": "", "orgs": [], "level": "", "mainOwner": "", "owners": [], "category": ""}, {"id": 90, "name": "fs-bi-export", "remark": "BI导出", "department": "数据业务技术BI组", "orgs": ["销售业务-BI"], "level": "L1", "mainOwner": "齐庆阳qiqingyang", "owners": ["翟付杰Jeffrey", "纪二飞Roy"], "category": "后台异步服务"}, {"id": 125, "name": "fs-broker-web", "remark": "开平openapi的web层", "department": "互联业务互联平台组", "orgs": ["互联业务-开平对接组", "互联业务-互联平台组"], "level": "L2", "mainOwner": "刘诗林liushilin", "owners": ["刘诗林liushilin", "曾令文Evan", "张诚fine", "钟兴ZhongXing"], "category": ""}, {"id": 2394, "name": "fs-erp-order-contacts-proxy", "remark": "连接器订单通讯录代理模块", "department": "互联业务集成平台组", "orgs": [], "level": "L1", "mainOwner": "冯院华Hardy", "owners": ["冯院华Hardy", "吴贝贝"], "category": "后台异步服务"}, {"id": 53, "name": "fs-active-session-manage", "remark": "用户票据管理", "department": "PaaS业务业务平台开发平台组", "orgs": [], "level": "L0", "mainOwner": "吴俊文Raymond", "owners": ["吴俊文Raymond", "刘一鸣Adam"], "category": "前台服务"}, {"id": 430, "name": "fs-plat-checknet-cgi", "remark": "检查网络服务_吴俊文", "department": "PaaS业务业务平台开发平台组", "orgs": ["销售协同-平台业务部"], "level": "L2", "mainOwner": "吴俊文Raymond", "owners": [], "category": "前台服务"}, {"id": 2338, "name": "fs-sail-order", "remark": "新版订货通服务", "department": "互联业务订货业务组", "orgs": ["互联业务-订货业务组"], "level": "L1", "mainOwner": "王凡", "owners": ["王凡", "袁杰<PERSON><PERSON>d", "陈柱深", "周保童Charles"], "category": "前台服务"}, {"id": 591, "name": "fs-xt-proxy", "remark": "xt 代理", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L3", "mainOwner": "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "owners": ["尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 2315, "name": "fs-httpproxy", "remark": "crmudobj.nscv.foneshare.cn代理", "department": "PaaS业务业务平台基础对象组", "orgs": [], "level": "", "mainOwner": "郑磊<PERSON><PERSON><PERSON>", "owners": ["郑磊<PERSON><PERSON><PERSON>", "周伟荣", "赵琚"], "category": "前台服务"}, {"id": 273, "name": "fs-mankeep-fsweb", "remark": "客脉项目纷享测我的名片,早期服务，现在不需要，服务已经下线", "department": "深研营销业务部", "orgs": ["互联业务-客脉业务组"], "level": "", "mainOwner": "郑辉Harry", "owners": ["郑辉Harry", "张胜斌zhangshengbin"], "category": ""}, {"id": 2360, "name": "fs-organization-provider-4data-auth", "remark": "为数据权限计算专门提供的组织架构服务", "department": "PaaS业务业务平台协同业务组", "orgs": ["销售协同-平台业务部"], "level": "L0", "mainOwner": "刘文绪dio", "owners": ["刘文绪dio", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "张文超"], "category": "前台服务"}, {"id": 522, "name": "fs-stone-cgi", "remark": "图片统一接入层 (目前主要承载企信、feeds的图片流量）", "department": "平台运营架构组", "orgs": [], "level": "L1", "mainOwner": "安宜龙Andy", "owners": ["安宜龙Andy"], "category": "前台服务"}, {"id": 111, "name": "fs-bi-task", "remark": "工作简报任务调度服务", "department": "PaaS业务业务平台协同业务组", "orgs": ["销售协同-企信", "销售协同-平台业务部"], "level": "L2", "mainOwner": "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "owners": ["尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>", "梁岩超Charles"], "category": "后台异步服务"}, {"id": 2528, "name": "fs-eye-manager", "remark": "可观测配置管理平台", "department": "平台运营架构组", "orgs": [], "level": "L3", "mainOwner": "严鑫Yan<PERSON>in", "owners": ["张恕征Zhang<PERSON>g", "严鑫Yan<PERSON>in"], "category": ""}, {"id": 371, "name": "fs-paas-gnomon-executor", "remark": "对象定时动作", "department": "PaaS业务基础平台流程组", "orgs": [], "level": "L1", "mainOwner": "宁天伟Ethan", "owners": ["万松<PERSON>", "郑子阳Julian", "梁楠liangnan"], "category": "后台异步服务"}, {"id": 466, "name": "fs-portal-provider", "remark": "支付后台管理系统", "department": "平台运营运维组", "orgs": ["互联业务-服务业务组"], "level": "L2", "mainOwner": "郭明Zero", "owners": ["丁成<PERSON><PERSON>", "郭明Zero", "谢向达", "丁成<PERSON><PERSON>"], "category": ""}, {"id": 337, "name": "fs-open-msg-all", "remark": "服务号消息投递", "department": "互联业务集成平台组", "orgs": ["互联业务-产品合作组"], "level": "", "mainOwner": "冯院华Hardy", "owners": ["冯院华Hardy"], "category": ""}, {"id": 517, "name": "fs-stage-propeller-cloud", "remark": "专属云阶段推进器模块", "department": "PaaS业务基础平台流程组", "orgs": [], "level": "L1", "mainOwner": "梁楠liangnan", "owners": ["梁楠liangnan"], "category": "合并部署服务"}, {"id": 2454, "name": "fs-stone-auth", "remark": "文件系统统一身份管理服务", "department": "平台运营架构组", "orgs": [], "level": "L0", "mainOwner": "安宜龙Andy", "owners": ["安宜龙Andy", "刘云松samuel"], "category": "前台服务"}, {"id": 2418, "name": "fs-task-manage-provider", "remark": "企信定时任务服务", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L2", "mainOwner": "梁岩超Charles", "owners": ["尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>", "梁岩超Charles"], "category": "后台异步服务"}, {"id": 36, "name": "config-web", "remark": "配置中心", "department": "平台运营架构组", "orgs": [], "level": "L3", "mainOwner": "李锐colin", "owners": ["李锐colin"], "category": ""}, {"id": 244, "name": "fs-jdy-web", "remark": "精斗云对接标准件web_陈祥飞", "department": "制造行业客开组", "orgs": [], "level": "L2", "mainOwner": "颜彩朵", "owners": [], "category": ""}, {"id": 2562, "name": "fs-marketing-crm", "remark": "营销业务", "department": "营销业务营销业务组", "orgs": ["互联业务-客脉业务组"], "level": "L2", "mainOwner": "袁建龙", "owners": ["郑辉Harry", "袁建龙", "周浮洋bruce", "罗勇Pate", "张树锋Halcyon", "张玉佳Mike"], "category": "后台异步服务"}, {"id": 327, "name": "fs-open-call-center-web", "remark": "呼叫中心-web层", "department": "平台运营运维组", "orgs": [], "level": "", "mainOwner": "郭明Zero", "owners": ["丁成<PERSON><PERSON>"], "category": ""}, {"id": 287, "name": "fs-meeting-assistant-biz<PERSON>r", "remark": "会议助手", "department": "消费品业务技术组", "orgs": ["销售协同-考外勤", "快销农牧行业组"], "level": "L2", "mainOwner": "贺政忠HeZhengzhong", "owners": ["贺政忠HeZhengzhong", "张华", "安丰胜Richard", "郭思卿", "张世民"], "category": ""}, {"id": 321, "name": "fs-open-app-center-provider", "remark": "应用中心系统", "department": "PaaS业务业务平台基础对象组", "orgs": [], "level": "L1", "mainOwner": "周伟荣", "owners": ["郑磊<PERSON><PERSON><PERSON>", "周伟荣"], "category": "前台服务"}, {"id": 2320, "name": "fs-oplog-recorder", "remark": "记录oplog测试mq分发框架", "department": "北研业务线", "orgs": [], "level": "L3", "mainOwner": "谷广田", "owners": ["李锐colin", "吴志辉<PERSON>Z<PERSON>hui", "谷广田"], "category": ""}, {"id": 592, "name": "fs-xtcrm-gateway-provider", "remark": "协同.NET服务代理", "department": "消费品业务技术销费组", "orgs": [], "level": "L2", "mainOwner": "杨庆飞", "owners": ["杨庆飞"], "category": "后台异步服务"}, {"id": 44, "name": "db-operation", "remark": "操作paas-pg数据库", "department": "北研业务线", "orgs": ["平台运营-PaaS"], "level": "", "mainOwner": "谷广田", "owners": ["李锐colin", "谷广田"], "category": ""}, {"id": 54, "name": "fs-addressbook-provider", "remark": "开平通讯录从虚拟机迁移到K8S", "department": "平台运营运维组", "orgs": [], "level": "", "mainOwner": "金哲玉", "owners": [], "category": ""}, {"id": 102, "name": "fs-bi-paas2gp-transfer", "remark": "paas数据同步到hive", "department": "数据业务技术平台组", "orgs": ["销售业务-BI"], "level": "L3", "mainOwner": "纪二飞Roy", "owners": ["纪二飞Roy", "赵孟华HuaZai"], "category": ""}, {"id": 265, "name": "fs-landray-oa-web", "remark": "之前客开项目，已经移交给项目组 吴永佳", "department": "互联业务集成平台组", "orgs": ["互联业务-开平对接组", "互联业务-产品合作组"], "level": "L2", "mainOwner": "吴永佳yongjia", "owners": ["吴永佳yongjia"], "category": "前台服务"}, {"id": 470, "name": "fs-qixin-bot-crm-helper", "remark": "企信-crm助手，助手订阅及消息推送等服务", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L2", "mainOwner": "梁岩超Charles", "owners": ["尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>", "梁岩超Charles"], "category": "前台服务"}, {"id": 105, "name": "fs-bi-refresh", "remark": "刷库服务", "department": "数据业务技术BI组", "orgs": ["销售业务-BI"], "level": "L2", "mainOwner": "翟付杰Jeffrey", "owners": ["纪二飞Roy", "翟付杰Jeffrey"], "category": ""}, {"id": 152, "name": "fs-crm-notify-provider", "remark": "crm提醒", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L2", "mainOwner": "<PERSON>硕<PERSON><PERSON>", "owners": ["尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>", "梁岩超Charles"], "category": "前台服务"}, {"id": 218, "name": "fs-flow-inspection", "remark": "流程数据巡检模块", "department": "PaaS业务基础平台流程组", "orgs": [], "level": "L3", "mainOwner": "宁天伟Ethan", "owners": ["崔永旭", "万松<PERSON>", "宁天伟Ethan", "刘畅CharonLucca", "梁楠liangnan"], "category": "后台异步服务"}, {"id": 248, "name": "fs-k3cloud-provider", "remark": "老K3对接插件主服务", "department": "互联业务集成平台组", "orgs": ["互联业务-开平对接组", "互联业务-产品合作组"], "level": "", "mainOwner": "冯院华Hardy", "owners": ["谢嘉裕Ken", "冯永亮Frank", "冯院华Hardy", "冯永亮Frank"], "category": ""}, {"id": 160, "name": "fs-crm-task-web", "remark": "CRM异步任务", "department": "制造行业开发组2", "orgs": [], "level": "L2", "mainOwner": "李秋林<PERSON>", "owners": ["李秋林<PERSON>", "袁杰<PERSON><PERSON>d", "王凡"], "category": "后台异步服务"}, {"id": 175, "name": "fs-dataplatform-lead-score-online-http", "remark": "线索打分服务", "department": "平台运营运维组", "orgs": [], "level": "", "mainOwner": "纪二飞Roy", "owners": ["纪二飞Roy"], "category": ""}, {"id": 330, "name": "fs-open-huawei-gateway", "remark": "CRM上架华为云代理服务", "department": "互联业务集成平台组", "orgs": [], "level": "L2", "mainOwner": "冯院华", "owners": ["冯院华"], "category": "后台异步服务"}, {"id": 462, "name": "fs-pod-service", "remark": "paas路由服务", "department": "PaaS业务基础平台元数据组", "orgs": [], "level": "L0", "mainOwner": "杨华国YHG", "owners": ["杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian"], "category": "后台异步服务"}, {"id": 2497, "name": "form-service", "remark": "工单", "department": "互联业务订货业务组", "orgs": [], "level": "L2", "mainOwner": "王凡", "owners": ["袁杰<PERSON><PERSON>d", "陈柱深", "王凡"], "category": "前台服务"}, {"id": 98, "name": "fs-bi-metadata", "remark": "负责人： 霍冬杰，翟付杰，刘强", "department": "数据业务技术BI组", "orgs": [], "level": "L1", "mainOwner": "罗江林luojianglin", "owners": ["翟付杰Jeffrey", "陈江飞chenjiangfei", "郑子阳Julian"], "category": "前台服务"}, {"id": 322, "name": "fs-open-app-center-web", "remark": "应用中心-web入口", "department": "PaaS业务业务平台基础对象组", "orgs": [], "level": "L2", "mainOwner": "周伟荣", "owners": ["郑磊<PERSON><PERSON><PERSON>", "周伟荣"], "category": "前台服务"}, {"id": 666, "name": "xxl-job-es-aggregate", "remark": "ES聚合任务执行器 负责人： 王百峰", "department": "平台运营运维组", "orgs": [], "level": "", "mainOwner": "金哲玉", "owners": [], "category": ""}, {"id": 2494, "name": "fs-fmcg-sales-mengniu-urgent", "remark": "蒙牛urgent集成环境", "department": "消费品业务技术创新组", "orgs": [], "level": "L2", "mainOwner": "吴佩峰wupeifeng", "owners": [], "category": ""}, {"id": 2482, "name": "fs-open-custom-hisense-decrypt-file", "remark": "客开-海信云文件加密、解密", "department": "制造行业客开组", "orgs": [], "level": "L2", "mainOwner": "颜彩朵", "owners": ["颜彩朵", "李秋林<PERSON>", "刘锴kay"], "category": "前台服务"}, {"id": 343, "name": "fs-open-warehouse-provider", "remark": "应用中心图片服务", "department": "平台运营运维组", "orgs": [], "level": "L2", "mainOwner": "李秋林<PERSON>", "owners": ["李秋林<PERSON>"], "category": ""}, {"id": 536, "name": "fs-sync-data-task", "remark": "数据同步服务任务", "department": "互联业务互联平台组", "orgs": ["互联业务-互联平台组"], "level": "L2", "mainOwner": "刘诗林liushilin", "owners": ["刘诗林liushilin", "张诚fine", "曾令文Evan", "钟兴ZhongXing"], "category": ""}, {"id": 64, "name": "fs-appserver-comment-web", "remark": "助手系列-评论系统", "department": "消费品业务技术组", "orgs": ["快销农牧行业组"], "level": "L2", "mainOwner": "贺政忠HeZhengzhong", "owners": ["贺政忠HeZhengzhong", "何静媛hejy"], "category": ""}, {"id": 110, "name": "fs-bi-stat-transfer", "remark": "4535151_刘强;程凯;高宠;王丽华_BI统计图迁移服务", "department": "数据业务技术BI组", "orgs": ["销售业务-BI"], "level": "L2", "mainOwner": "翟付杰Jeffrey", "owners": ["翟付杰Jeffrey", "陈江飞chenjiangfei"], "category": ""}, {"id": 2389, "name": "fs-document-convert-big", "remark": "大型文档处理", "department": "平台运营架构组", "orgs": [], "level": "L2", "mainOwner": "安宜龙Andy", "owners": ["安宜龙Andy", "刘云松samuel"], "category": ""}, {"id": 208, "name": "fs-feeds-biz", "remark": "Feed接入层", "department": "PaaS业务业务平台协同业务组", "orgs": ["销售协同-平台业务部"], "level": "L1", "mainOwner": "张文超", "owners": ["张文超", "刘文绪dio", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 636, "name": "open-api-gateway-web-file", "remark": "openapi-网关文件服务", "department": "互联业务互联平台组", "orgs": [], "level": "L2", "mainOwner": "刘诗林liushilin", "owners": ["刘诗林liushilin", "张诚fine", "曾令文Evan", "钟兴ZhongXing"], "category": ""}, {"id": 2545, "name": "short-url-sync-debug", "remark": "短链同步到蒙牛云，为防止异常流量独立拆分服务并进行限流", "department": "平台运营架构组", "orgs": [], "level": "L2", "mainOwner": "刘全胜liuquansheng", "owners": ["刘全胜liuquansheng"], "category": "前台服务"}, {"id": 5, "name": "action-router-console", "remark": "灰度路由数据管理平台", "department": "平台运营架构组", "orgs": ["平台运营-平台架构部"], "level": "L3", "mainOwner": "侯世鹏", "owners": ["侯世鹏"], "category": ""}, {"id": 213, "name": "fs-feeds-search-datamgr-index", "remark": "主站feeds搜索", "department": "深研营销业务部", "orgs": [], "level": "", "mainOwner": "郑辉Harry", "owners": ["郑辉Harry"], "category": ""}, {"id": 452, "name": "fs-plat-service-biz", "remark": "社交业务、管理后台服务", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L2", "mainOwner": "刘文绪dio", "owners": ["刘文绪dio", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "张文超"], "category": "前台服务"}, {"id": 211, "name": "fs-feeds-search-broker-adapter", "remark": "主站feeds搜索broker", "department": "深研营销业务部", "orgs": ["互联业务-客脉业务组"], "level": "", "mainOwner": "郑辉Harry", "owners": ["郑辉Harry"], "category": ""}, {"id": 2538, "name": "fs-out-identity-provider", "remark": "", "department": "深研营销业务部", "orgs": [], "level": "L2", "mainOwner": "郑辉Harry", "owners": ["郑辉Harry"], "category": "前台服务"}, {"id": 438, "name": "fs-plat-login-cloud", "remark": "登录服务", "department": "PaaS业务业务平台开发平台组", "orgs": ["销售协同-平台业务部"], "level": "L0", "mainOwner": "丁龙飞<PERSON><PERSON>", "owners": ["吴俊文Raymond", "丁龙飞<PERSON><PERSON>"], "category": "前台服务"}, {"id": 490, "name": "fs-qixin-task-provider", "remark": "任务2.0灰度服务", "department": "PaaS业务业务平台开发平台组", "orgs": [], "level": "L2", "mainOwner": "梁岩超Charles", "owners": ["尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>", "梁岩超Charles"], "category": "前台服务"}, {"id": 12, "name": "async-job-export", "remark": "异步导入导出", "department": "平台运营架构组", "orgs": ["平台运营-PaaS", "销售业务-BI", "平台运营-平台架构部"], "level": "L1", "mainOwner": "谷广田", "owners": ["谷广田", "吴志辉<PERSON>Z<PERSON>hui"], "category": "后台异步服务"}, {"id": 2551, "name": "bi-console-compose", "remark": "", "department": "数据业务技术BI组", "orgs": [], "level": "L2", "mainOwner": "翟付杰Jeffrey", "owners": ["翟付杰Jeffrey"], "category": "前台服务"}, {"id": 2468, "name": "fs-bi-industry", "remark": "工商数据平台", "department": "数据业务技术平台组", "orgs": [], "level": "L1", "mainOwner": "赵正豪zhenghao", "owners": ["纪二飞Roy", "赵正豪zhenghao"], "category": "前台服务"}, {"id": 2548, "name": "fs-eye-radar", "remark": "蜂眼雷达服务，按需启用，比如ping服务", "department": "平台运营架构组", "orgs": [], "level": "L3", "mainOwner": "李锐colin", "owners": ["李锐colin", "吴志辉<PERSON>Z<PERSON>hui"], "category": "后台异步服务"}, {"id": 361, "name": "fs-paas-bizconf-web", "remark": "通用配置bizconf服务", "department": "PaaS业务基础平台元数据组", "orgs": [], "level": "L0", "mainOwner": "钱凌锋irony", "owners": ["杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian"], "category": "前台服务"}, {"id": 441, "name": "fs-plat-netdisk-provider", "remark": "网盘服务", "department": "平台运营架构组", "orgs": ["销售协同-平台业务部", "平台运营-平台架构部"], "level": "L2", "mainOwner": "刘云松samuel", "owners": ["安宜龙Andy"], "category": ""}, {"id": 444, "name": "fs-plat-organization-cloud", "remark": "通讯录服务", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L0", "mainOwner": "刘文绪dio", "owners": ["刘文绪dio", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "张文超"], "category": "前台服务"}, {"id": 2484, "name": "fs-qixin-file1", "remark": "企信file专属云独立部署，仅能单节点部署", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L2", "mainOwner": "梁岩超Charles", "owners": ["梁岩超Charles"], "category": "前台服务"}, {"id": 2522, "name": "fs-crm-sfa-cloud-gray", "remark": "SFA服务专属云灰度", "department": "平台运营架构组", "orgs": ["发布系统-管理员", "销售业务-SFA"], "level": "L2", "mainOwner": "陈金典Kimd", "owners": ["陈金典Kimd", "任林波Ren<PERSON><PERSON>bo"], "category": "前台服务"}, {"id": 224, "name": "fs-fmcg-sales-cgi", "remark": "快消订单", "department": "消费品业务技术销费组", "orgs": ["快销农牧行业组"], "level": "L1", "mainOwner": "吴佩峰wupeifeng", "owners": ["杨庆飞", "贺政忠HeZhengzhong", "杨亚兴", "林明杰", "吴永鑫"], "category": "前台服务"}, {"id": 297, "name": "fs-metadata-for-refresh", "remark": "pg中的数据刷入ES", "department": "PaaS业务基础平台元数据组", "orgs": ["平台运营-PaaS", "平台运营-平台架构部"], "level": "L3", "mainOwner": "李晨Jason", "owners": ["杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian"], "category": "后台异步服务"}, {"id": 324, "name": "fs-open-assets-all", "remark": "fs-open-assets-all", "department": "制造行业开发组2", "orgs": [], "level": "L2", "mainOwner": "李秋林<PERSON>", "owners": ["李秋林<PERSON>"], "category": "前台服务"}, {"id": 570, "name": "fs-warehouse-processor", "remark": "文件转移回收", "department": "平台运营架构组", "orgs": [], "level": "L3", "mainOwner": "安宜龙Andy", "owners": [], "category": ""}, {"id": 639, "name": "paas-bi-copier", "remark": "paas到bi数据同步", "department": "数据业务技术平台组", "orgs": ["平台运营-PaaS"], "level": "L2", "mainOwner": "赵正豪zhenghao", "owners": ["吴志辉<PERSON>Z<PERSON>hui", "谷广田", "郑子阳Julian", "赵正豪zhenghao", "纪二飞Roy"], "category": ""}, {"id": 78, "name": "fs-bi-biz", "remark": "工作简报业务接口", "department": "平台运营运维组", "orgs": ["销售协同-企信"], "level": "L2", "mainOwner": "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "owners": ["尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>", "梁岩超Charles"], "category": ""}, {"id": 229, "name": "fs-fxiaoke-connector", "remark": "4635287_客户同步业务_李清波", "department": "SFA业务技术售中技术组", "orgs": [], "level": "L3", "mainOwner": "陈金典Kimd", "owners": ["吴地厚"], "category": ""}, {"id": 234, "name": "fs-hubble-query", "remark": "全局搜索接口", "department": "PaaS业务基础平台元数据组", "orgs": [], "level": "L1", "mainOwner": "吕杰lvjie", "owners": ["杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian"], "category": "前台服务"}, {"id": 52, "name": "fs-account-statement-web", "remark": "企业互联对账单", "department": "互联业务订货业务组", "orgs": [], "level": "", "mainOwner": "王凡", "owners": ["王凡", "陈柱深", "袁杰<PERSON><PERSON>d"], "category": ""}, {"id": 2579, "name": "fs-marketing-intelligence", "remark": "", "department": "", "orgs": [], "level": "", "mainOwner": "", "owners": [], "category": ""}, {"id": 355, "name": "fs-paas-app-udobj-rest", "remark": "udobj的rest接口", "department": "PaaS业务业务平台基础对象组", "orgs": ["销售业务-自定义对象"], "level": "L0", "mainOwner": "郑磊<PERSON><PERSON><PERSON>", "owners": ["郑磊<PERSON><PERSON><PERSON>", "周伟荣", "赵琚", "王毅Wang<PERSON>i", "冯津<PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 475, "name": "fs-qixin-ext-compose", "remark": "企信合并服务", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L2", "mainOwner": "梁岩超Charles", "owners": ["尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>", "梁岩超Charles"], "category": "前台服务"}, {"id": 6, "name": "action-router-service", "remark": "灰度路由服务", "department": "平台运营架构组", "orgs": [], "level": "L2", "mainOwner": "侯世鹏", "owners": ["侯世鹏"], "category": "前台服务"}, {"id": 2348, "name": "checkins-v2-mq-dispatcher", "remark": "外勤 v2 调度", "department": "消费品业务技术外勤组", "orgs": ["快销农牧行业组", "平台运营-PaaS"], "level": "L2", "mainOwner": "张世民", "owners": ["贺政忠HeZhengzhong", "何静媛hejy", "郭思卿", "张世民", "张晓攀"], "category": ""}, {"id": 2306, "name": "fs-bi-dev-platform", "remark": "BI开发环境", "department": "数据业务技术BI组", "orgs": [], "level": "L1", "mainOwner": "齐庆阳qiqingyang", "owners": ["翟付杰Jeffrey", "邹俊杰Json", "齐庆阳qiqingyang", "陈江飞chenjiangfei", "贾福鹏jiafupeng"], "category": "前台服务"}, {"id": 2410, "name": "fs-organization-sqlserver-proxy", "remark": "通讯录同步mds服务", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "", "mainOwner": "刘文绪dio", "owners": ["陈晓彬", "刘文绪dio", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "category": ""}, {"id": 2582, "name": "fs-bi-udf-report-offline", "remark": "", "department": "", "orgs": [], "level": "", "mainOwner": "", "owners": [], "category": ""}, {"id": 227, "name": "fs-fsc-provider", "remark": "文件服务", "department": "平台运营架构组", "orgs": [], "level": "L2", "mainOwner": "安宜龙Andy", "owners": ["吴志辉<PERSON>Z<PERSON>hui", "安宜龙Andy"], "category": ""}, {"id": 2388, "name": "fs-paas-recording-provider", "remark": "收藏、关注、访问历史", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L2", "mainOwner": "<PERSON>硕<PERSON><PERSON>", "owners": ["<PERSON>硕<PERSON><PERSON>", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 2319, "name": "fs-open-custom", "remark": "客开项目", "department": "制造行业客开组", "orgs": [], "level": "L2", "mainOwner": "颜彩朵", "owners": ["颜彩朵", "李秋林<PERSON>", "戴云"], "category": "前台服务"}, {"id": 2519, "name": "fs-paas-function-service-runtime-01", "remark": "函数服务", "department": "PaaS业务业务平台开发平台组", "orgs": ["销售业务-自定义对象", "销售协同-平台业务部"], "level": "L2", "mainOwner": "吴俊文Raymond", "owners": ["吴俊文Raymond", "斯作益seth"], "category": "前台服务"}, {"id": 45, "name": "elastic-job-console", "remark": "elaticjob控制台，目前bi在用", "department": "北研业务线", "orgs": [], "level": "", "mainOwner": "翟付杰Jeffrey", "owners": ["翟付杰Jeffrey"], "category": ""}, {"id": 2457, "name": "erpdss-outer-connector", "remark": "集成平台外部连接器", "department": "互联业务集成平台组", "orgs": ["互联业务-开平对接组", "互联业务-产品合作组"], "level": "L1", "mainOwner": "冯院华Hardy", "owners": ["冯院华Hardy", "谢嘉裕Ken", "郭远东", "陈晓彬", "柯南颖"], "category": ""}, {"id": 300, "name": "fs-metadata-option", "remark": "元数据option服务", "department": "PaaS业务基础平台元数据组", "orgs": [], "level": "L0", "mainOwner": "钱凌锋irony", "owners": ["杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian"], "category": "前台服务"}, {"id": 328, "name": "fs-open-center-fcp-provider", "remark": "应用中心手机端入口", "department": "PaaS业务业务平台基础对象组", "orgs": [], "level": "", "mainOwner": "周伟荣", "owners": ["郑磊<PERSON><PERSON><PERSON>", "周伟荣"], "category": ""}, {"id": 56, "name": "fs-apibus-global", "remark": "内部服务调用全局网关，承载几乎所有内部调用", "department": "PaaS业务业务平台部", "orgs": [], "level": "L0", "mainOwner": "郑磊<PERSON><PERSON><PERSON>", "owners": ["王毅Wang<PERSON>i", "郑磊<PERSON><PERSON><PERSON>", "赵琚"], "category": "前台服务"}, {"id": 2581, "name": "fs-bi-stat-offline", "remark": "", "department": "", "orgs": [], "level": "", "mainOwner": "", "owners": [], "category": ""}, {"id": 350, "name": "fs-organization-biz", "remark": "通讯录biz服务", "department": "PaaS业务业务平台协同业务组", "orgs": ["销售协同-平台业务部"], "level": "L1", "mainOwner": "刘文绪dio", "owners": ["刘文绪dio", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 486, "name": "fs-qixin-search-provider", "remark": "企信搜索", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L2", "mainOwner": "梁岩超Charles", "owners": ["尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>", "梁岩超Charles"], "category": ""}, {"id": 267, "name": "fs-leica-web", "remark": "crm查重服务", "department": "平台运营架构组", "orgs": [], "level": "L2", "mainOwner": "谷广田", "owners": ["谷广田"], "category": ""}, {"id": 279, "name": "fs-marketing-provider", "remark": "营销通provider服务", "department": "营销业务营销业务组", "orgs": [], "level": "L1", "mainOwner": "郑辉Harry", "owners": ["郑辉Harry", "罗勇Pate", "张树锋Halcyon", "周浮洋bruce", "张玉佳Mike"], "category": "前台服务"}, {"id": 342, "name": "fs-open-sales-forecast-web", "remark": "销售预测", "department": "制造行业开发组2", "orgs": [], "level": "L2", "mainOwner": "李秋林<PERSON>", "owners": ["李秋林<PERSON>"], "category": ""}, {"id": 601, "name": "i18n-service", "remark": "多语服务Rest Api服务", "department": "PaaS业务基础平台元数据组", "orgs": ["平台运营-平台架构部", "PaaS-国际化"], "level": "L1", "mainOwner": "梁梓闻liang<PERSON><PERSON>", "owners": ["杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian"], "category": "前台服务"}, {"id": 61, "name": "fs-appserver-checkins-v2", "remark": "外勤签到服务-vip top60环境", "department": "消费品业务技术外勤组", "orgs": ["快销农牧行业组"], "level": "L1", "mainOwner": "贺政忠HeZhengzhong", "owners": ["贺政忠HeZhengzhong", "郭思卿", "张世民", "何静媛hejy"], "category": "前台服务"}, {"id": 2, "name": "fs-bi-transfer", "remark": "元数据迁移服务", "department": "数据业务技术BI组", "orgs": ["销售业务-BI"], "level": "L2", "mainOwner": "翟付杰Jeffrey", "owners": ["翟付杰Jeffrey", "陈江飞chenjiangfei", "郑子阳Julian"], "category": ""}, {"id": 121, "name": "fs-bpm-cloud", "remark": "专属云,业务流程模块", "department": "PaaS业务基础平台流程组", "orgs": ["销售业务-流程组"], "level": "L1", "mainOwner": "梁楠liangnan", "owners": ["郑子阳Julian", "崔永旭", "万松<PERSON>", "梁楠liangnan", "刘畅CharonLucca"], "category": "合并部署服务"}, {"id": 163, "name": "fs-crm-workflow", "remark": "审批流服务", "department": "PaaS业务基础平台流程组", "orgs": ["销售业务-流程组"], "level": "L1", "mainOwner": "崔永旭", "owners": ["郑磊<PERSON><PERSON><PERSON>", "崔永旭", "万松<PERSON>", "宁天伟Ethan", "梁楠liangnan", "刘畅CharonLucca", "郑子阳Julian"], "category": "前台服务"}, {"id": 2500, "name": "fs-crm-cloud", "remark": "fs-crm专属云沙盒", "department": "互联业务订货业务组", "orgs": [], "level": "L2", "mainOwner": "陈柱深", "owners": ["袁杰<PERSON><PERSON>d"], "category": "前台服务"}, {"id": 178, "name": "fs-dataplatform-source-flag-graph", "remark": "4638305_数据平台任务关系导入neo4j_ 负责人：吴丕华", "department": "数据业务技术平台组", "orgs": ["销售业务-BI"], "level": "", "mainOwner": "纪二飞Roy", "owners": [], "category": ""}, {"id": 351, "name": "fs-organization-provider", "remark": "通讯录数据层", "department": "PaaS业务业务平台协同业务组", "orgs": ["销售协同-平台业务部"], "level": "L0", "mainOwner": "刘文绪dio", "owners": ["刘文绪dio", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 2390, "name": "fs-feishu-web", "remark": "飞书连接器web模块", "department": "互联业务集成平台组", "orgs": [], "level": "L1", "mainOwner": "冯院华Hardy", "owners": ["冯院华Hardy", "吴贝贝"], "category": "前台服务"}, {"id": 225, "name": "fs-fmcg-service", "remark": "快消服务", "department": "消费品业务技术销费组", "orgs": ["快销农牧行业组"], "level": "L2", "mainOwner": "林明杰", "owners": ["杨庆飞", "贺政忠HeZhengzhong", "杨亚兴", "林明杰", "吴永鑫"], "category": "后台异步服务"}, {"id": 258, "name": "fs-kis-connector-task", "remark": "金蝶KIS对应应用任务调度", "department": "平台运营运维组", "orgs": ["互联业务-开平对接组", "互联业务-客脉业务组", "互联业务-产品合作组"], "level": "L2", "mainOwner": "冯院华Hardy", "owners": ["冯院华Hardy"], "category": ""}, {"id": 2349, "name": "fs-open-center-app-fcp-provider", "remark": "应用中心手机端入口", "department": "PaaS业务业务平台基础对象组", "orgs": [], "level": "L1", "mainOwner": "周伟荣", "owners": ["李秋林<PERSON>", "郑磊<PERSON><PERSON><PERSON>", "周伟荣"], "category": "前台服务"}, {"id": 2563, "name": "egress-proxy-datax", "remark": "", "department": "", "orgs": [], "level": "", "mainOwner": "", "owners": [], "category": ""}, {"id": 2542, "name": "fmcg-data-strategy-server", "remark": "", "department": "消费品业务技术外勤组", "orgs": ["快销农牧行业组"], "level": "L3", "mainOwner": "张晓攀", "owners": ["张晓攀"], "category": "合并部署服务"}, {"id": 108, "name": "fs-bi-sqlgenerator", "remark": "CRM-BI-SQL生成器", "department": "数据业务技术平台组", "orgs": [], "level": "L2", "mainOwner": "翟付杰Jeffrey", "owners": ["纪二飞Roy", "翟付杰Jeffrey"], "category": ""}, {"id": 112, "name": "fs-bi-task-new", "remark": "CRM-BI-新订阅服务", "department": "数据业务技术BI组", "orgs": [], "level": "L2", "mainOwner": "翟付杰Jeffrey", "owners": ["翟付杰Jeffrey"], "category": ""}, {"id": 2362, "name": "fs-organziation-adapter-4orgbiz", "remark": "通讯录adapter", "department": "PaaS业务业务平台协同业务组", "orgs": ["销售协同-平台业务部"], "level": "L1", "mainOwner": "刘文绪dio", "owners": ["刘文绪dio", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "张文超"], "category": "前台服务"}, {"id": 2337, "name": "fs-plat-feeds-next-cloud", "remark": "feed服务(去sqlserver的)", "department": "PaaS业务业务平台协同业务组", "orgs": ["销售协同-平台业务部"], "level": "L2", "mainOwner": "张文超", "owners": ["张文超", "刘文绪dio", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 507, "name": "fs-scheduler-task-provider", "remark": "计划任务", "department": "PaaS业务业务平台基础对象组", "orgs": [], "level": "L0", "mainOwner": "李远", "owners": ["李远", "郑磊<PERSON><PERSON><PERSON>", "周伟荣"], "category": "后台异步服务"}, {"id": 153, "name": "fs-crm-provider", "remark": "开平老B1", "department": "互联业务互联平台组", "orgs": ["互联业务-开平对接组"], "level": "", "mainOwner": "钟兴ZhongXing", "owners": ["冯永亮Frank", "钟兴ZhongXing"], "category": ""}, {"id": 2447, "name": "fs-fmcg-public-abutment", "remark": "对接公网", "department": "消费品业务技术创新组", "orgs": ["快销农牧行业组"], "level": "L2", "mainOwner": "杨亚兴", "owners": ["贺政忠HeZhengzhong", "杨亚兴"], "category": ""}, {"id": 626, "name": "jenkins-msg", "remark": "负责人： 张新宇", "department": "PaaS业务基础测试组", "orgs": [], "level": "L3", "mainOwner": "吴志辉<PERSON>Z<PERSON>hui", "owners": ["王栋轩", "白琳", "刘亚庆"], "category": "后台异步服务"}, {"id": 242, "name": "fs-jdy-provider", "remark": "精斗云对接", "department": "制造行业客开组", "orgs": [], "level": "L2", "mainOwner": "冯院华Hardy", "owners": ["冯院华Hardy", "陈宗鑫chenzongxin"], "category": ""}, {"id": 2326, "name": "fs-open-qywx-save-message", "remark": "企业微信会话存档", "department": "互联业务集成平台组", "orgs": [], "level": "L1", "mainOwner": "冯院华Hardy", "owners": ["柯南颖"], "category": "后台异步服务"}, {"id": 2526, "name": "fs-paas-process-orch", "remark": "流程OneFlow业务层调用引擎独立部署的环境", "department": "PaaS业务基础平台流程组", "orgs": [], "level": "L0", "mainOwner": "万松<PERSON>", "owners": ["万松<PERSON>"], "category": "前台服务"}, {"id": 443, "name": "fs-plat-org-management", "remark": "管理后台，人员对象、部门对象服务", "department": "PaaS业务业务平台协同业务组", "orgs": ["销售协同-平台业务部"], "level": "L2", "mainOwner": "刘文绪dio", "owners": ["张文超", "刘文绪dio", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 11, "name": "async-job-center-cloud", "remark": "导入导出（谷广田）+批量处理工具（杨华国）", "department": "PaaS业务基础平台元数据组", "orgs": ["销售业务-BI", "平台运营-平台架构部"], "level": "L2", "mainOwner": "谷广田", "owners": ["谷广田", "杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian", "李锐colin"], "category": "合并部署服务"}, {"id": 42, "name": "data-auth-worker", "remark": "数据权限计算服务", "department": "PaaS业务基础平台元数据组", "orgs": [], "level": "L0", "mainOwner": "杨华国YHG", "owners": ["杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian", "李锐colin"], "category": "后台异步服务"}, {"id": 104, "name": "fs-bi-provider", "remark": "工作简报", "department": "PaaS业务业务平台协同业务组", "orgs": ["销售协同-企信", "销售协同-平台业务部"], "level": "L2", "mainOwner": "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "owners": ["梁岩超Charles", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 228, "name": "fs-fsc4stone", "remark": "文件服务-专属云文件服务网关", "department": "平台运营架构组", "orgs": [], "level": "L0", "mainOwner": "安宜龙Andy", "owners": ["安宜龙Andy", "吴志辉<PERSON>Z<PERSON>hui"], "category": "合并部署服务"}, {"id": 201, "name": "fs-ext-contact-provider", "remark": "外部联系人", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L2", "mainOwner": "<PERSON>硕<PERSON><PERSON>", "owners": ["尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>", "梁岩超Charles"], "category": "前台服务"}, {"id": 460, "name": "fs-plat-webhook-provider", "remark": "外部开通服务", "department": "PaaS业务业务平台开发平台组", "orgs": ["销售协同-平台业务部"], "level": "L1", "mainOwner": "吴俊文Raymond", "owners": ["吴俊文Raymond", "陈宗鑫chenzongxin"], "category": "后台异步服务"}, {"id": 210, "name": "fs-feeds-provider", "remark": "Java协同服务", "department": "PaaS业务业务平台协同业务组", "orgs": ["销售协同-平台业务部"], "level": "L1", "mainOwner": "张文超", "owners": ["张文超", "刘文绪dio", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 271, "name": "fs-loki-router", "remark": "Grafana-Loki 路由服务, 责任人：吴志辉", "department": "", "orgs": [], "level": "L3", "mainOwner": "", "owners": [], "category": ""}, {"id": 531, "name": "fs-stone-thumbnail-proxy", "remark": "新版文件系统异步缩略", "department": "平台运营架构组", "orgs": [], "level": "L2", "mainOwner": "安宜龙Andy", "owners": ["安宜龙Andy"], "category": ""}, {"id": 534, "name": "fs-stone4cloud", "remark": "企业文件（stone）系统合并部署服务", "department": "平台运营架构组", "orgs": [], "level": "L0", "mainOwner": "安宜龙Andy", "owners": ["吴志辉<PERSON>Z<PERSON>hui", "安宜龙Andy"], "category": "合并部署服务"}, {"id": 131, "name": "fs-changchai-connector", "remark": "4632270_常柴客开项目-导出txt文件_郭远东", "department": "互联业务集成平台组", "orgs": ["互联业务-开平对接组"], "level": "", "mainOwner": "刘显诗", "owners": ["刘显诗"], "category": ""}, {"id": 2458, "name": "fs-crm-fmcg-wq-gray", "remark": "云环境 fmcg-wq 快消外勤对象灰度", "department": "消费品业务技术外勤组", "orgs": [], "level": "L2", "mainOwner": "张世民", "owners": ["张世民", "贺政忠HeZhengzhong", "何静媛hejy", "郭思卿"], "category": ""}, {"id": 166, "name": "fs-crm-workflow-processor", "remark": "审批流消息处理服务", "department": "PaaS业务基础平台流程组", "orgs": ["销售业务-流程组"], "level": "L1", "mainOwner": "崔永旭", "owners": ["崔永旭", "万松<PERSON>", "宁天伟Ethan", "梁楠liangnan", "刘畅CharonLucca"], "category": "后台异步服务"}, {"id": 193, "name": "fs-enterprise-relation-biz", "remark": "互联平台服务", "department": "互联业务互联平台组", "orgs": ["互联业务-互联平台组"], "level": "L2", "mainOwner": "曾文汶sunny", "owners": ["曾文汶sunny", "钟兴ZhongXing", "韦宁宁Jack<PERSON><PERSON>"], "category": "前台服务"}, {"id": 504, "name": "fs-sail-server", "remark": "订货通", "department": "互联业务订货业务组", "orgs": ["互联业务-订货业务组"], "level": "L2", "mainOwner": "王凡", "owners": ["王凡", "袁杰<PERSON><PERSON>d", "陈柱深", "周保童Charles"], "category": "前台服务"}, {"id": 543, "name": "fs-training", "remark": "培训助手", "department": "营销业务营销业务组", "orgs": [], "level": "L2", "mainOwner": "张玉佳Mike", "owners": ["郑辉Harry", "张玉佳Mike"], "category": "合并部署服务"}, {"id": 664, "name": "weex-config-console", "remark": "卡梅隆、weex资源包发布管理控制台", "department": "平台运营架构组", "orgs": ["平台运营-PaaS", "平台运营-平台架构部"], "level": "L3", "mainOwner": "侯世鹏", "owners": ["侯世鹏"], "category": ""}, {"id": 2435, "name": "erp-connector-proxy", "remark": "集成平台请求海外erp服务", "department": "互联业务集成平台组", "orgs": [], "level": "L1", "mainOwner": "冯院华Hardy", "owners": ["陈晓彬", "冯院华Hardy", "谢嘉裕Ken", "郭远东", "柯南颖", "陈宗鑫chenzongxin"], "category": "后台异步服务"}, {"id": 100, "name": "fs-bi-mq", "remark": "CRM-BI-新FSBI服务-mq服务", "department": "数据业务技术BI组", "orgs": [], "level": "L2", "mainOwner": "罗江林luojianglin", "owners": ["翟付杰Jeffrey", "陈江飞chenjiangfei", "郑子阳Julian"], "category": ""}, {"id": 198, "name": "fs-es-admin", "remark": "es管理系统", "department": "北研业务线", "orgs": [], "level": "", "mainOwner": "李锐colin", "owners": ["李锐colin"], "category": ""}, {"id": 524, "name": "fs-stone-dataserver", "remark": "企业文件（stone）数据存储服务", "department": "平台运营架构组", "orgs": [], "level": "L0", "mainOwner": "安宜龙Andy", "owners": ["安宜龙Andy"], "category": "前台服务"}, {"id": 518, "name": "fs-stage-propeller-processor", "remark": "阶段推进器消息处理服务", "department": "PaaS业务基础平台流程组", "orgs": ["平台运营-PaaS"], "level": "L1", "mainOwner": "梁楠liangnan", "owners": ["崔永旭", "万松<PERSON>", "宁天伟Ethan", "梁楠liangnan", "刘畅CharonLucca", "梁楠liangnan"], "category": "后台异步服务"}, {"id": 552, "name": "fs-uc-provider", "remark": "用户中心", "department": "PaaS业务业务平台开发平台组", "orgs": [], "level": "L2", "mainOwner": "吴俊文Raymond", "owners": ["陈晓彬", "吴俊文Raymond", "丁龙飞<PERSON><PERSON>"], "category": "前台服务"}, {"id": 644, "name": "paas-leica-sync", "remark": "自定义查重服务", "department": "PaaS业务基础平台元数据组", "orgs": ["平台运营-PaaS", "销售业务-自定义对象"], "level": "L0", "mainOwner": "李晨Jason", "owners": ["杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian"], "category": "合并部署服务"}, {"id": 655, "name": "qywx-event-handler-web", "remark": "企业微信代理 - 事件处理", "department": "互联业务集成平台组", "orgs": ["互联业务-产品合作组"], "level": "L1", "mainOwner": "冯院华Hardy", "owners": ["冯院华Hardy"], "category": "后台异步服务"}, {"id": 109, "name": "fs-bi-stat", "remark": "自定义统计图 负责人： 于少博,程凯,王荣飞,牛铜铜,王丽华", "department": "数据业务技术BI组", "orgs": [], "level": "L1", "mainOwner": "贾福鹏jiafupeng", "owners": ["翟付杰Jeffrey", "陈江飞chenjiangfei", "郑子阳Julian", "贾福鹏jiafupeng", "肖扬Jack", "齐庆阳qiqingyang"], "category": "前台服务"}, {"id": 157, "name": "fs-crm-smartform", "remark": "智能表单服务", "department": "PaaS业务业务平台基础对象组", "orgs": ["销售业务-自定义对象"], "level": "L2", "mainOwner": "周伟荣", "owners": ["周伟荣", "王毅Wang<PERSON>i", "郑磊<PERSON><PERSON><PERSON>", "赵琚", "冯津<PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 2532, "name": "fs-crm-task-sfa-ai", "remark": "activity相关后台业务", "department": "SFA业务高服行业技术组", "orgs": [], "level": "L1", "mainOwner": "任海涛koopa", "owners": ["任海涛koopa"], "category": ""}, {"id": 2424, "name": "fs-paas-ai-provider", "remark": "paas aigc服务", "department": "PaaS业务业务平台开发平台组", "orgs": [], "level": "L1", "mainOwner": "吴俊文Raymond", "owners": ["王亚豪Thorne", "斯作益seth", "刘一鸣Adam", "张晓峰zxf", "吴俊文Raymond", "丁龙飞<PERSON><PERSON>"], "category": "前台服务"}, {"id": 270, "name": "fs-log-ui", "remark": "简易日志中心", "department": "北研业务线", "orgs": [], "level": "", "mainOwner": "谷广田", "owners": ["谷广田"], "category": ""}, {"id": 2333, "name": "fs-new-schedule", "remark": "日程对象", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L2", "mainOwner": "<PERSON>硕<PERSON><PERSON>", "owners": ["<PERSON>硕<PERSON><PERSON>", "梁岩超Charles", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "张文超"], "category": "前台服务"}, {"id": 407, "name": "fs-pay-luckmoney", "remark": "支付服务", "department": "制造行业开发组1", "orgs": [], "level": "L2", "mainOwner": "郭明Zero", "owners": ["谢向达", "丁成<PERSON><PERSON>", "冯永亮Frank"], "category": ""}, {"id": 119, "name": "fs-bpm-after-action", "remark": "后动作服务", "department": "PaaS业务基础平台流程组", "orgs": [], "level": "L0", "mainOwner": "崔永旭", "owners": ["崔永旭", "万松<PERSON>", "梁楠liangnan", "刘畅CharonLucca"], "category": "前台服务"}, {"id": 2313, "name": "fs-erp-sync-data-custom", "remark": "ERP数据同步客开服务", "department": "互联业务集成平台组", "orgs": [], "level": "L1", "mainOwner": "冯院华Hardy", "owners": ["冯院华Hardy", "冯永亮Frank"], "category": "后台异步服务"}, {"id": 236, "name": "fs-image-server", "remark": "头像CDN回源服务", "department": "平台运营架构组", "orgs": [], "level": "L2", "mainOwner": "安宜龙Andy", "owners": ["安宜龙Andy"], "category": ""}, {"id": 590, "name": "fs-xshell-compose", "remark": "内部shell管理系统合集", "department": "平台运营架构组", "orgs": [], "level": "L3", "mainOwner": "吴志辉<PERSON>Z<PERSON>hui", "owners": ["李锐colin", "吴志辉<PERSON>Z<PERSON>hui"], "category": ""}, {"id": 2552, "name": "fs-paas-process-orch-task", "remark": "Flow专用引擎processor", "department": "PaaS业务基础平台流程组", "orgs": ["平台运营-PaaS", "销售业务-流程组"], "level": "L0", "mainOwner": "宁天伟Ethan", "owners": [], "category": "后台异步服务"}, {"id": 2493, "name": "fs-sms-platform", "remark": "短信平台", "department": "营销业务营销业务组", "orgs": [], "level": "L1", "mainOwner": "张玉佳Mike", "owners": ["郑辉Harry", "张玉佳Mike"], "category": "合并部署服务"}, {"id": 2525, "name": "egress-api-gateway", "remark": "egress-api-gateway", "department": "平台运营架构组", "orgs": [], "level": "", "mainOwner": "刘全胜liuquansheng", "owners": ["吴志辉<PERSON>Z<PERSON>hui", "刘全胜liuquansheng"], "category": ""}, {"id": 2449, "name": "egress-proxy-service", "remark": "跨云和外网服务代理", "department": "平台运营架构组", "orgs": ["平台运营-平台架构部"], "level": "L2", "mainOwner": "刘全胜liuquansheng", "owners": ["李锐colin", "吴志辉<PERSON>Z<PERSON>hui", "刘全胜liuquansheng"], "category": ""}, {"id": 117, "name": "fs-big-file-manager-biz", "remark": "大文件管理后台", "department": "平台运营架构组", "orgs": [], "level": "L2", "mainOwner": "刘云松samuel", "owners": ["安宜龙Andy", "刘云松samuel"], "category": ""}, {"id": 154, "name": "fs-crm-recycling-task", "remark": "crm回收服务", "department": "SFA业务售前组", "orgs": ["销售业务-SFA"], "level": "L1", "mainOwner": "陈金典Kimd", "owners": ["龚春如", "陈金典Kimd", "任林波Ren<PERSON><PERSON>bo", "赵鹏欣Paul"], "category": "后台异步服务"}, {"id": 379, "name": "fs-paas-org", "remark": "paas-组织架构&用户组组件", "department": "PaaS业务基础平台元数据组", "orgs": [], "level": "L0", "mainOwner": "钱凌锋irony", "owners": ["杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian"], "category": "前台服务"}, {"id": 482, "name": "fs-qixin-push-broker", "remark": "第三方推送broker", "department": "PaaS业务业务平台协同业务组", "orgs": ["销售协同-企信"], "level": "L2", "mainOwner": "梁岩超Charles", "owners": ["梁岩超Charles", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>"], "category": "后台异步服务"}, {"id": 24, "name": "auditlog-service", "remark": "crm的审计日志", "department": "PaaS业务业务平台基础对象组", "orgs": ["销售业务-自定义对象"], "level": "L2", "mainOwner": "李远", "owners": ["郑磊<PERSON><PERSON><PERSON>", "王毅Wang<PERSON>i", "李远"], "category": "前台服务"}, {"id": 167, "name": "fs-cross-enterprise-sharedisk", "remark": "互联企业网盘", "department": "互联业务互联平台组", "orgs": ["互联业务-互联平台组"], "level": "L2", "mainOwner": "钟兴ZhongXing", "owners": ["钟兴ZhongXing"], "category": ""}, {"id": 276, "name": "fs-mankeep-provider", "remark": "客脉项目服务", "department": "营销业务营销业务组", "orgs": ["互联业务-客脉业务组"], "level": "L1", "mainOwner": "郑辉Harry", "owners": ["郑辉Harry", "罗勇Pate", "张树锋Halcyon", "周浮洋bruce", "张玉佳Mike"], "category": "后台异步服务"}, {"id": 285, "name": "fs-marketing-web", "remark": "营销通web服务", "department": "营销业务营销业务组", "orgs": ["互联业务-客脉业务组", "互联业务-互联平台组"], "level": "L1", "mainOwner": "郑辉Harry", "owners": ["郑辉Harry", "罗勇Pate", "张树锋Halcyon", "周浮洋bruce", "张玉佳Mike"], "category": "前台服务"}, {"id": 620, "name": "ibss-site-dc", "remark": "dcx接受客户端日志", "department": "北研业务线", "orgs": [], "level": "", "mainOwner": "谷广田", "owners": ["吴志辉<PERSON>Z<PERSON>hui", "谷广田"], "category": ""}, {"id": 212, "name": "fs-feeds-search-datamgr-cluster", "remark": "feeds搜索数据调度服务", "department": "深研营销业务部", "orgs": [], "level": "", "mainOwner": "郑辉Harry", "owners": [], "category": ""}, {"id": 349, "name": "fs-organization-adapter-hj", "remark": "通讯录对外接口-汇聚用", "department": "PaaS业务业务平台协同业务组", "orgs": ["销售协同-平台业务部"], "level": "L2", "mainOwner": "刘文绪dio", "owners": ["刘文绪dio", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 514, "name": "fs-social-task", "remark": "social task服务", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L2", "mainOwner": "刘文绪dio", "owners": ["张文超", "刘文绪dio", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "category": "后台异步服务"}, {"id": 568, "name": "fs-warehouse-enterprise-statistic", "remark": "文件服务：文件使用统计", "department": "平台运营架构组", "orgs": [], "level": "L3", "mainOwner": "安宜龙Andy", "owners": ["安宜龙Andy"], "category": ""}, {"id": 94, "name": "fs-bi-industry-interface", "remark": "工商查询k8s接口", "department": "数据业务技术平台组", "orgs": ["销售业务-BI", "平台运营-平台架构部"], "level": "L1", "mainOwner": "纪二飞Roy", "owners": ["纪二飞Roy", "赵孟华HuaZai", "赵正豪zhenghao", "郑子阳Julian"], "category": "前台服务"}, {"id": 2512, "name": "fs-data-copy", "remark": "跨云跨租户的数据备份、复制、对比平台", "department": "北研业务线", "orgs": [], "level": "L2", "mainOwner": "李锐colin", "owners": ["李锐colin"], "category": "前台服务"}, {"id": 206, "name": "fs-fcp-sample", "remark": "fcp协议样例", "department": "平台运营运维组", "orgs": [], "level": "", "mainOwner": "金哲玉", "owners": [], "category": ""}, {"id": 295, "name": "fs-message-wechat", "remark": "消息推送平台服务", "department": "PaaS业务业务平台协同业务组", "orgs": ["销售协同-企信"], "level": "L2", "mainOwner": "<PERSON>硕<PERSON><PERSON>", "owners": ["尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>", "梁岩超Charles"], "category": "前台服务"}, {"id": 282, "name": "fs-marketing-statistic-cache-provider", "remark": "4667491_营销通统计模块缓存_肖帆", "department": "平台运营运维组", "orgs": ["互联业务-客脉业务组"], "level": "", "mainOwner": "金哲玉", "owners": [], "category": ""}, {"id": 289, "name": "fs-message-external", "remark": "外部通知消息", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L2", "mainOwner": "<PERSON>硕<PERSON><PERSON>", "owners": ["尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>", "梁岩超Charles"], "category": "前台服务"}, {"id": 2527, "name": "fs-paas-ai-task", "remark": "纷享 AI 统一封装接口平台，以及提供向量数据库访问能力", "department": "PaaS业务业务平台开发平台组", "orgs": [], "level": "L2", "mainOwner": "斯作益seth", "owners": ["斯作益seth"], "category": "后台异步服务"}, {"id": 2308, "name": "fs-flow-processor", "remark": "流程公用模块消息处理", "department": "PaaS业务基础平台流程组", "orgs": ["销售业务-流程组", "平台运营-PaaS"], "level": "L1", "mainOwner": "宁天伟Ethan", "owners": ["崔永旭", "万松<PERSON>", "刘畅CharonLucca", "宁天伟Ethan", "梁楠liangnan"], "category": "后台异步服务"}, {"id": 223, "name": "fs-fmcg-efficiency-cgi-urgent", "remark": "快消客开-审核服务-urgent环境", "department": "消费品业务技术外勤组", "orgs": ["快销农牧行业组"], "level": "L2", "mainOwner": "贺政忠HeZhengzhong", "owners": ["贺政忠HeZhengzhong", "杨亚兴", "杨庆飞", "张常清zhangchangqing"], "category": ""}, {"id": 235, "name": "fs-id-server", "remark": "全局唯一id生成器 ", "department": "北研业务线", "orgs": [], "level": "L2", "mainOwner": "吴志辉<PERSON>Z<PERSON>hui", "owners": ["李锐colin", "吴志辉<PERSON>Z<PERSON>hui"], "category": ""}, {"id": 240, "name": "fs-integral-task", "remark": "积分任务调度服务", "department": "消费品业务技术销费组", "orgs": ["快销农牧行业组"], "level": "L2", "mainOwner": "李光田liguangtian", "owners": ["李光田liguangtian"], "category": ""}, {"id": 2358, "name": "fs-qixin-file", "remark": "企信移动端文件服务", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L2", "mainOwner": "梁岩超Charles", "owners": ["尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>", "梁岩超Charles"], "category": "前台服务"}, {"id": 284, "name": "fs-marketing-task", "remark": "任务调度", "department": "深研营销业务部", "orgs": ["互联业务-客脉业务组", "互联业务-互联平台组"], "level": "L2", "mainOwner": "郑辉Harry", "owners": ["郑辉Harry", "罗勇Pate", "张树锋Halcyon", "周浮洋bruce", "张玉佳Mike"], "category": "后台异步服务"}, {"id": 298, "name": "fs-metadata-for-test", "remark": "元数据测试服务", "department": "PaaS业务基础平台元数据组", "orgs": ["平台运营-PaaS"], "level": "L3", "mainOwner": "钱凌锋irony", "owners": ["李锐colin", "杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian", "王栋轩"], "category": "后台异步服务"}, {"id": 345, "name": "fs-open-webhook-accountsync", "remark": "云之家账号同步", "department": "互联业务集成平台组", "orgs": ["互联业务-开平对接组", "互联业务-产品合作组"], "level": "L2", "mainOwner": "冯院华Hardy", "owners": ["柯南颖", "冯院华"], "category": "后台异步服务"}, {"id": 82, "name": "fs-bi-crm-report-web", "remark": "BI-老报表|老统计图", "department": "数据业务技术BI组", "orgs": [], "level": "L1", "mainOwner": "陈江飞chenjiangfei", "owners": ["翟付杰Jeffrey", "邹俊杰Json", "齐庆阳qiqingyang", "陈江飞chenjiangfei", "贾福鹏jiafupeng", "郑子阳Julian", "贾宇奇jiayuqi"], "category": "前台服务"}, {"id": 269, "name": "fs-livingroom", "remark": "4651767_客脉管理后台_肖帆", "department": "深研营销业务部", "orgs": ["互联业务-客脉业务组"], "level": "L3", "mainOwner": "郑辉Harry", "owners": ["郑辉Harry", "张树锋Halcyon", "周浮洋bruce", "张玉佳Mike"], "category": ""}, {"id": 293, "name": "fs-message-server-web", "remark": "消息平台HTTP接口", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L1", "mainOwner": "<PERSON>硕<PERSON><PERSON>", "owners": ["梁岩超Charles", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>"], "category": "后台异步服务"}, {"id": 555, "name": "fs-user-extension-provider", "remark": "App自定义服务-服务提供", "department": "PaaS业务业务平台基础对象组", "orgs": [], "level": "L1", "mainOwner": "周伟荣", "owners": ["郑磊<PERSON><PERSON><PERSON>", "周伟荣"], "category": "前台服务"}, {"id": 551, "name": "fs-uc-biz", "remark": "账号中心与终端交互层", "department": "PaaS业务业务平台开发平台组", "orgs": ["销售协同-平台业务部"], "level": "L2", "mainOwner": "吴俊文Raymond", "owners": ["吴俊文Raymond"], "category": "前台服务"}, {"id": 2439, "name": "fs-ui-paas-cloud", "remark": "web自定义app自定义", "department": "PaaS业务业务平台基础对象组", "orgs": [], "level": "L1", "mainOwner": "周伟荣", "owners": ["郑磊<PERSON><PERSON><PERSON>", "周伟荣"], "category": "合并部署服务"}, {"id": 2488, "name": "fs-checkins-biz-gray", "remark": "外勤统计服务-灰度环境", "department": "消费品业务技术外勤组", "orgs": [], "level": "L2", "mainOwner": "张世民", "owners": ["贺政忠HeZhengzhong", "何静媛hejy", "郭思卿", "张世民"], "category": ""}, {"id": 250, "name": "fs-k3cloud-web", "remark": "老K3对接插件 web服务", "department": "互联业务集成平台组", "orgs": [], "level": "L1", "mainOwner": "冯院华Hardy", "owners": ["谢嘉裕Ken", "冯永亮Frank", "冯院华Hardy", "冯永亮Frank"], "category": "前台服务"}, {"id": 358, "name": "fs-paas-auth-provider", "remark": "paas-功能权限组件（包括应用功能，功能权限，字段权限，视图权限，角色，用户角色服务）", "department": "PaaS业务业务平台协同业务组", "orgs": ["销售协同-平台业务部"], "level": "L0", "mainOwner": "刘文绪dio", "owners": ["刘文绪dio", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 506, "name": "fs-salarybill", "remark": "工资助手服务", "department": "互联业务互联平台组", "orgs": ["互联业务-互联平台组"], "level": "L2", "mainOwner": "钟兴ZhongXing", "owners": ["冯永亮Frank", "钟兴ZhongXing"], "category": ""}, {"id": 49, "name": "fast-notifier", "remark": "快速消息通知通道，通常用语广播缓存清理消息", "department": "平台运营架构组", "orgs": [], "level": "L0", "mainOwner": "谷广田", "owners": ["吴志辉<PERSON>Z<PERSON>hui", "李锐colin", "谷广田"], "category": "后台异步服务"}, {"id": 526, "name": "fs-stone-fileserver", "remark": "企业文件（stone）业务处理服务", "department": "平台运营架构组", "orgs": [], "level": "L0", "mainOwner": "安宜龙Andy", "owners": ["安宜龙Andy"], "category": "前台服务"}, {"id": 2536, "name": "fs-bi-agent", "remark": "BI-Agent", "department": "数据业务技术BI组", "orgs": [], "level": "L2", "mainOwner": "翟付杰Jeffrey", "owners": ["翟付杰Jeffrey"], "category": ""}, {"id": 667, "name": "yarn-manager", "remark": "大数据任务状态监控", "department": "数据业务技术平台组", "orgs": ["销售业务-BI"], "level": "L3", "mainOwner": "纪二飞Roy", "owners": ["纪二飞Roy", "赵孟华HuaZai", "赵正豪zhenghao", "肖扬Jack"], "category": ""}, {"id": 619, "name": "ibss-service-xms-provider", "remark": "汇聚销客产品功能配置服务", "department": "平台运营运维组", "orgs": ["销售业务-SFA"], "level": "L3", "mainOwner": "陈金典Kimd", "owners": ["龚春如", "陈金典Kimd"], "category": "后台异步服务"}, {"id": 2363, "name": "datax-sync", "remark": "支持直接往目标库写数据版本的datax", "department": "平台运营架构组", "orgs": [], "level": "L2", "mainOwner": "侯世鹏", "owners": ["侯世鹏"], "category": ""}, {"id": 2561, "name": "fs-dingtalk-isv-gray", "remark": "", "department": "平台运营架构组", "orgs": [], "level": "", "mainOwner": "柯南颖", "owners": [], "category": ""}, {"id": 2446, "name": "fs-global-transaction", "remark": "简易分布式事务框架", "department": "PaaS业务业务平台基础对象组", "orgs": [], "level": "", "mainOwner": "赵琚", "owners": ["赵琚", "周伟荣", "郑磊<PERSON><PERSON><PERSON>"], "category": ""}, {"id": 489, "name": "fs-qixin-task-brush-db-provider", "remark": "任务刷库服务", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L3", "mainOwner": "梁岩超Charles", "owners": ["尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>", "梁岩超Charles"], "category": "前台服务"}, {"id": 553, "name": "fs-uc-rest-proxy", "remark": "uc的rest服务", "department": "PaaS业务业务平台开发平台组", "orgs": [], "level": "", "mainOwner": "吴俊文Raymond", "owners": ["吴俊文Raymond"], "category": "前台服务"}, {"id": 50, "name": "fs-account-statement", "remark": "企业互联-对账单应用", "department": "互联业务订货业务组", "orgs": [], "level": "L2", "mainOwner": "王凡", "owners": ["王凡", "陈柱深", "袁杰<PERSON><PERSON>d"], "category": "前台服务"}, {"id": 140, "name": "fs-console-compose", "remark": "各种内部管理系统合集", "department": "PaaS业务基础平台流程组", "orgs": ["销售业务-SFA", "销售业务-流程组", "平台运营-PaaS"], "level": "L3", "mainOwner": "万松<PERSON>", "owners": ["万松<PERSON>", "李磊Leo", "郑子阳Julian", "梁梓闻liang<PERSON><PERSON>"], "category": "合并部署服务"}, {"id": 164, "name": "fs-crm-workflow-cloud", "remark": "专属云 审批流模块", "department": "PaaS业务基础平台流程组", "orgs": [], "level": "L1", "mainOwner": "崔永旭", "owners": ["郑子阳Julian", "崔永旭", "万松<PERSON>", "宁天伟Ethan", "梁楠liangnan", "刘畅CharonLucca"], "category": "合并部署服务"}, {"id": 2379, "name": "fs-qixin-console", "remark": "qixin-console", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L3", "mainOwner": "梁岩超Charles", "owners": ["梁岩超Charles", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>"], "category": "前台服务"}, {"id": 159, "name": "fs-crm-task-sfa", "remark": "crm任务", "department": "SFA业务技术组", "orgs": ["互联业务-订货业务组", "销售业务-自定义对象", "销售业务-SFA"], "level": "L1", "mainOwner": "任林波Ren<PERSON><PERSON>bo", "owners": ["龚春如", "赵鹏欣Paul", "任林波Ren<PERSON><PERSON>bo", "吉明哲daemon", "高光荣gaoguangrong", "孙得育", "陈金典Kimd"], "category": "后台异步服务"}, {"id": 2466, "name": "metadata-console-compose", "remark": "paas元数据权限团队的所有console类服务", "department": "PaaS业务基础平台元数据组", "orgs": [], "level": "L3", "mainOwner": "钱凌锋irony", "owners": ["杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian", "何金富Hejinfu"], "category": "合并部署服务"}, {"id": 641, "name": "paas-datax-pusher", "remark": "PAAS数据同步服务；数据监听和发送端", "department": "平台运营架构组", "orgs": [], "level": "L2", "mainOwner": "侯世鹏", "owners": ["侯世鹏"], "category": ""}, {"id": 2374, "name": "fs-erp-oa", "remark": "用来同步crm待办提醒", "department": "互联业务集成平台组", "orgs": [], "level": "L1", "mainOwner": "冯院华Hardy", "owners": ["冯院华Hardy", "柯南颖"], "category": "后台异步服务"}, {"id": 215, "name": "fs-feishu-connector", "remark": "飞书对接项目", "department": "SFA业务技术售中技术组", "orgs": [], "level": "", "mainOwner": "宋长乾", "owners": [], "category": ""}, {"id": 263, "name": "fs-kiscloud-web", "remark": "kis云数据同步", "department": "互联业务集成平台组", "orgs": [], "level": "L2", "mainOwner": "冯院华Hardy", "owners": ["冯院华Hardy", "柯南颖"], "category": "前台服务"}, {"id": 171, "name": "fs-dataload-web", "remark": "万马文件传输项目 负责人： 高鑫", "department": "平台运营运维组", "orgs": ["互联业务-开平对接组"], "level": "", "mainOwner": "金哲玉", "owners": [], "category": ""}, {"id": 577, "name": "fs-webpage-customer-provider", "remark": "应用首页", "department": "PaaS业务业务平台基础对象组", "orgs": [], "level": "L0", "mainOwner": "周伟荣", "owners": ["王毅Wang<PERSON>i", "郑磊<PERSON><PERSON><PERSON>", "周伟荣"], "category": "前台服务"}, {"id": 2298, "name": "fs-crm-fmcg-service", "remark": "快消CRM服务", "department": "消费品业务技术销费组", "orgs": ["快销农牧行业组"], "level": "L1", "mainOwner": "杨庆飞", "owners": ["杨庆飞", "贺政忠HeZhengzhong", "杨亚兴", "林明杰", "吴永鑫", "李光田liguangtian", "<PERSON>京<PERSON>"], "category": "前台服务"}, {"id": 2431, "name": "fs-paas-app-task-all", "remark": "app-task和calculate-task的合集", "department": "PaaS业务业务平台基础对象组", "orgs": [], "level": "", "mainOwner": "赵琚", "owners": ["周伟荣", "郑磊<PERSON><PERSON><PERSON>"], "category": ""}, {"id": 2402, "name": "fs-paas-function-template", "remark": "函数模板服务", "department": "PaaS业务业务平台开发平台组", "orgs": [], "level": "L2", "mainOwner": "斯作益seth", "owners": ["斯作益seth"], "category": "前台服务"}, {"id": 476, "name": "fs-qixin-extension-provider", "remark": "企信扩展服务", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L2", "mainOwner": "梁岩超Charles", "owners": ["尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>", "梁岩超Charles"], "category": "前台服务"}, {"id": 2303, "name": "data-auth-service-bi", "remark": "数据权限鉴权服务；专门给BI使用", "department": "PaaS业务基础平台元数据组", "orgs": [], "level": "L0", "mainOwner": "杨华国YHG", "owners": ["杨华国YHG", "李磊Leo", "郑子阳Julian", "李锐colin"], "category": "前台服务"}, {"id": 2547, "name": "fast-notifier-qixin", "remark": "为企信提供独立的notifier", "department": "平台运营架构组", "orgs": [], "level": "L2", "mainOwner": "谷广田", "owners": ["谷广田", "李锐colin"], "category": "前台服务"}, {"id": 2329, "name": "fs-dingtalk-all", "remark": "用来同步crm待办提醒到钉钉消息", "department": "互联业务集成平台组", "orgs": [], "level": "L1", "mainOwner": "冯院华Hardy", "owners": ["柯南颖"], "category": "后台异步服务"}, {"id": 369, "name": "fs-paas-function-service-runtime", "remark": "函数灰度服务", "department": "PaaS业务业务平台开发平台组", "orgs": ["销售业务-自定义对象", "销售协同-平台业务部"], "level": "L0", "mainOwner": "斯作益seth", "owners": ["斯作益seth", "吴俊文Raymond", "刘一鸣Adam"], "category": "前台服务"}, {"id": 37, "name": "crm-bi-custom-statistic", "remark": "统计图服务", "department": "数据业务技术平台组", "orgs": [], "level": "L2", "mainOwner": "纪二飞Roy", "owners": ["纪二飞Roy", "翟付杰Jeffrey"], "category": "后台异步服务"}, {"id": 243, "name": "fs-jdy-task", "remark": "精斗云对接", "department": "制造行业客开组", "orgs": [], "level": "L2", "mainOwner": "冯院华Hardy", "owners": ["冯院华Hardy", "陈宗鑫chenzongxin"], "category": ""}, {"id": 413, "name": "fs-pay-tob", "remark": "支付服务", "department": "制造行业开发组1", "orgs": [], "level": "L2", "mainOwner": "郭明Zero", "owners": ["谢向达", "丁成<PERSON><PERSON>", "郭明Zero"], "category": ""}, {"id": 512, "name": "fs-sms", "remark": "短信发送-已迁移至egress-api-service", "department": "互联业务互联平台组", "orgs": [], "level": "", "mainOwner": "吴志辉<PERSON>Z<PERSON>hui", "owners": ["吴志辉<PERSON>Z<PERSON>hui"], "category": ""}, {"id": 505, "name": "fs-sail-wechat", "remark": "订货通web服务层", "department": "互联业务订货业务组", "orgs": ["互联业务-订货业务组", "互联业务-互联应用组"], "level": "", "mainOwner": "王凡", "owners": ["王凡", "袁杰<PERSON><PERSON>d", "陈柱深", "周保童Charles"], "category": ""}, {"id": 583, "name": "fs-wechat-union-all", "remark": "微联服务号业务", "department": "互联业务互联平台组", "orgs": [], "level": "L2", "mainOwner": "封金运fengjy", "owners": ["曾文汶sunny", "钟兴ZhongXing"], "category": ""}, {"id": 653, "name": "qywx-account-bind-provider", "remark": "企业微信代理 - 账号绑定", "department": "互联业务集成平台组", "orgs": ["互联业务-产品合作组"], "level": "L1", "mainOwner": "冯院华Hardy", "owners": ["冯院华"], "category": "后台异步服务"}, {"id": 35, "name": "config-service", "remark": "配置中心", "department": "平台运营架构组", "orgs": [], "level": "L1", "mainOwner": "侯世鹏", "owners": ["李锐colin", "侯世鹏"], "category": "后台异步服务"}, {"id": 2334, "name": "fs-new-schedule-async", "remark": "日程对象异步服务", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L2", "mainOwner": "<PERSON>硕<PERSON><PERSON>", "owners": ["尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>", "梁岩超Charles"], "category": "后台异步服务"}, {"id": 2490, "name": "fs-open-qywx-all", "remark": "专属云企微连接器服务", "department": "互联业务集成平台组", "orgs": [], "level": "L1", "mainOwner": "陈宗鑫chenzongxin", "owners": ["陈宗鑫chenzongxin"], "category": "合并部署服务"}, {"id": 2546, "name": "fs-sail-order-task", "remark": "新版订货通mq消费服务", "department": "互联业务订货业务组", "orgs": [], "level": "L2", "mainOwner": "周保童Charles", "owners": ["王凡", "周保童Charles", "袁杰<PERSON><PERSON>d"], "category": "后台异步服务"}, {"id": 2479, "name": "fs-paas-app-udobj-rest-02", "remark": "fs-paas-app-udobj-rest-02", "department": "PaaS业务业务平台基础对象组", "orgs": [], "level": "L0", "mainOwner": "郑磊<PERSON><PERSON><PERSON>", "owners": ["郑磊<PERSON><PERSON><PERSON>", "周伟荣", "赵琚", "王毅Wang<PERSON>i", "冯津<PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 2314, "name": "fs-recognizecard", "remark": "名片王对接老服务（待下线）", "department": "消费品业务技术销费组", "orgs": [], "level": "L2", "mainOwner": "杨庆飞", "owners": ["杨庆飞"], "category": "前台服务"}, {"id": 2377, "name": "fs-support", "remark": "NPS、意见反馈、帮助中心", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L2", "mainOwner": "<PERSON>硕<PERSON><PERSON>", "owners": ["<PERSON>硕<PERSON><PERSON>", "梁岩超Charles", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 2575, "name": "egress-proxy-mq-proxy", "remark": "", "department": "", "orgs": [], "level": "", "mainOwner": "", "owners": [], "category": ""}, {"id": 65, "name": "fs-appserver-holiday-v2", "remark": "假期助手", "department": "消费品业务技术外勤组", "orgs": [], "level": "L2", "mainOwner": "张世民", "owners": ["贺政忠HeZhengzhong", "郭思卿", "张世民"], "category": ""}, {"id": 170, "name": "fs-customer-contract-provider", "remark": "信息收集", "department": "PaaS业务基础平台流程组", "orgs": ["平台运营-PaaS"], "level": "", "mainOwner": "万松<PERSON>", "owners": ["万松<PERSON>"], "category": "后台异步服务"}, {"id": 277, "name": "fs-mankeep-task", "remark": "客脉项目定时任务", "department": "深研营销业务部", "orgs": ["互联业务-客脉业务组"], "level": "L2", "mainOwner": "郑辉Harry", "owners": ["郑辉Harry", "罗勇Pate", "张树锋Halcyon", "周浮洋bruce", "张玉佳Mike"], "category": ""}, {"id": 28, "name": "checkins-office-stat-job", "remark": "考外勤统计内网服务", "department": "消费品业务技术外勤组", "orgs": ["快销农牧行业组"], "level": "L3", "mainOwner": "张世民", "owners": ["贺政忠HeZhengzhong", "何静媛hejy", "郭思卿", "张世民"], "category": ""}, {"id": 196, "name": "fs-erp-sync-data", "remark": "ERP数据同步主服务ERP数据同步主服务【已和Hardy确认，运维早班无需关注；深研有值班同事专门关注】", "department": "互联业务集成平台组", "orgs": [], "level": "L1", "mainOwner": "冯院华Hardy", "owners": ["冯院华Hardy", "谢嘉裕Ken", "郭远东", "柯南颖", "冯永亮Frank", "陈晓彬"], "category": "后台异步服务"}, {"id": 203, "name": "fs-eye-consumer", "remark": "蜂眼监控-消费kafka日志", "department": "平台运营架构组", "orgs": [], "level": "L3", "mainOwner": "谷广田", "owners": ["李锐colin", "吴志辉<PERSON>Z<PERSON>hui", "谷广田", "张恕征Zhang<PERSON>g"], "category": ""}, {"id": 656, "name": "qywx-message-send-provider", "remark": "企业微信代理 - 发消息模块", "department": "互联业务集成平台组", "orgs": ["互联业务-产品合作组"], "level": "L1", "mainOwner": "冯院华Hardy", "owners": ["冯院华Hardy"], "category": "后台异步服务"}, {"id": 485, "name": "fs-qixin-search-message-provider", "remark": "企信消息搜索", "department": "PaaS业务业务平台协同业务组", "orgs": ["销售协同-企信"], "level": "L2", "mainOwner": "梁岩超Charles", "owners": ["尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>", "梁岩超Charles"], "category": ""}, {"id": 513, "name": "fs-social-feeds", "remark": "协同和 paas 的中间层", "department": "PaaS业务业务平台协同业务组", "orgs": ["销售协同-平台业务部"], "level": "L1", "mainOwner": "张文超", "owners": ["张文超", "刘文绪dio", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "买年顺"], "category": "前台服务"}, {"id": 115, "name": "fs-bi-uitype", "remark": "BI新的uitype服务", "department": "数据业务技术BI组", "orgs": ["销售业务-BI"], "level": "L1", "mainOwner": "罗江林luojianglin", "owners": ["陈江飞chenjiangfei", "翟付杰Jeffrey", "郑子阳Julian"], "category": "前台服务"}, {"id": 2456, "name": "fs-crm-fmcg-service-gray", "remark": "蒙牛灰度环境", "department": "消费品业务技术销费组", "orgs": ["快销农牧行业组"], "level": "L2", "mainOwner": "杨庆飞", "owners": ["杨庆飞", "林明杰", "<PERSON>京<PERSON>", "吴永鑫"], "category": ""}, {"id": 186, "name": "fs-dingtalk-web", "remark": "钉钉对接插件", "department": "互联业务集成平台组", "orgs": [], "level": "L1", "mainOwner": "冯院华Hardy", "owners": ["柯南颖", "冯院华"], "category": "前台服务"}, {"id": 2323, "name": "fs-paas-wishful", "remark": "NPS，OCR，函数模板，电子签功能合集", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L2", "mainOwner": "<PERSON>硕<PERSON><PERSON>", "owners": ["李远", "<PERSON>硕<PERSON><PERSON>", "梁岩超Charles", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 91, "name": "fs-bi-feed2pg-online", "remark": "feed销售记录数据同步到BI pg库", "department": "数据业务技术平台组", "orgs": [], "level": "L2", "mainOwner": "纪二飞Roy", "owners": ["纪二飞Roy"], "category": ""}, {"id": 233, "name": "fs-hubble-index", "remark": "全局搜索数据同步", "department": "PaaS业务基础平台元数据组", "orgs": [], "level": "L1", "mainOwner": "吕杰lvjie", "owners": ["杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian"], "category": "后台异步服务"}, {"id": 260, "name": "fs-kiscloud-consumer", "remark": "KIS云对接的数据推送的MQ消费服务", "department": "互联业务集成平台组", "orgs": [], "level": "L2", "mainOwner": "冯院华Hardy", "owners": ["冯院华Hardy", "柯南颖"], "category": "后台异步服务"}, {"id": 2511, "name": "fs-paas-app-udobj-02", "remark": "fs-paas-app-udobj-02", "department": "PaaS业务业务平台基础对象组", "orgs": [], "level": "L0", "mainOwner": "郑磊<PERSON><PERSON><PERSON>", "owners": ["郑磊<PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 2577, "name": "dts-didi-api", "remark": "", "department": "", "orgs": [], "level": "", "mainOwner": "", "owners": [], "category": ""}, {"id": 80, "name": "fs-bi-compose", "remark": "BI多模块合并部署工程", "department": "数据业务技术BI组", "orgs": [], "level": "L1", "mainOwner": "翟付杰Jeffrey", "owners": ["翟付杰Jeffrey", "邹俊杰Json", "齐庆阳qiqingyang", "陈江飞chenjiangfei", "贾福鹏jiafupeng"], "category": "前台服务"}, {"id": 202, "name": "fs-eye-alarm", "remark": "蜂眼监控告警", "department": "", "orgs": [], "level": "", "mainOwner": "", "owners": [], "category": ""}, {"id": 220, "name": "fs-fmcg-customized-biz", "remark": "快消自定义功能", "department": "消费品业务技术创新组", "orgs": ["快销农牧行业组"], "level": "L2", "mainOwner": "贺政忠HeZhengzhong", "owners": ["贺政忠HeZhengzhong", "杨庆飞", "张晓攀", "何静媛hejy", "吴永鑫"], "category": ""}, {"id": 2477, "name": "fs-sentinel-manager", "remark": "Sentinel 规则管理", "department": "平台运营架构组", "orgs": [], "level": "L2", "mainOwner": "吴志辉<PERSON>Z<PERSON>hui", "owners": ["吴志辉<PERSON>Z<PERSON>hui", "刘全胜liuquansheng"], "category": "前台服务"}, {"id": 2357, "name": "fs-3rd-service", "remark": "调用第三方服务封装", "department": "平台运营架构组", "orgs": [], "level": "L3", "mainOwner": "谷广田", "owners": ["李锐colin", "吴志辉<PERSON>Z<PERSON>hui", "谷广田"], "category": ""}, {"id": 2322, "name": "fs-crm-import-manufacturing", "remark": "制造行业预制对象导入服务", "department": "制造行业开发组2", "orgs": [], "level": "L2", "mainOwner": "李秋林<PERSON>", "owners": ["丁成<PERSON><PERSON>", "郭明Zero", "谢向达", "戴云", "李秋林<PERSON>", "颜彩朵", "韦建韩Barry", "杨希luke"], "category": "后台异步服务"}, {"id": 2341, "name": "fs-open-dingtalk", "remark": "用来同步crm待办提醒到钉钉消息", "department": "互联业务集成平台组", "orgs": [], "level": "L1", "mainOwner": "陈宗鑫chenzongxin", "owners": [], "category": "合并部署服务"}, {"id": 395, "name": "fs-paas-workflow-processor", "remark": "引擎消息处理-灰度服务", "department": "PaaS业务基础平台流程组", "orgs": ["平台运营-PaaS", "销售业务-流程组"], "level": "L0", "mainOwner": "万松<PERSON>", "owners": ["崔永旭", "万松<PERSON>", "宁天伟Ethan", "梁楠liangnan", "刘畅CharonLucca"], "category": "后台异步服务"}, {"id": 572, "name": "fs-warehouse-statistic", "remark": "文件统计服务", "department": "平台运营架构组", "orgs": [], "level": "L3", "mainOwner": "安宜龙Andy", "owners": ["安宜龙Andy"], "category": ""}, {"id": 2543, "name": "outer-oa-connector-web", "remark": "集成平台OA统一基座", "department": "互联业务集成平台组", "orgs": [], "level": "L2", "mainOwner": "柯南颖", "owners": ["柯南颖", "陈宗鑫chenzongxin", "冯院华Hardy", "陈晓彬"], "category": "前台服务"}, {"id": 2355, "name": "bi-cus-server", "remark": "", "department": "数据业务技术BI组", "orgs": [], "level": "L2", "mainOwner": "翟付杰Jeffrey", "owners": ["翟付杰Jeffrey"], "category": ""}, {"id": 247, "name": "fs-jsapi-fcpserver", "remark": "jsapi-fcp服务层", "department": "互联业务互联平台组", "orgs": ["互联业务-互联平台组"], "level": "L2", "mainOwner": "钟兴ZhongXing", "owners": ["钟兴ZhongXing"], "category": ""}, {"id": 2347, "name": "fs-open-huawei-imc-sync", "remark": "华为imc市场的数据同步", "department": "互联业务集成平台组", "orgs": [], "level": "L2", "mainOwner": "冯院华Hardy", "owners": ["柯南颖", "冯院华Hardy"], "category": "后台异步服务"}, {"id": 533, "name": "fs-stone-upload-consumer", "remark": "文件系统上传处理MQ消息", "department": "平台运营架构组", "orgs": [], "level": "L2", "mainOwner": "安宜龙Andy", "owners": ["安宜龙Andy"], "category": ""}, {"id": 162, "name": "fs-crm-udobj", "remark": "自定义对象", "department": "PaaS业务业务平台基础对象组", "orgs": [], "level": "L2", "mainOwner": "郑磊<PERSON><PERSON><PERSON>", "owners": ["郑磊<PERSON><PERSON><PERSON>", "吴健鹏Wyatt", "周伟荣", "赵琚", "李磊Leo", "王毅Wang<PERSON>i", "郑子阳Julian"], "category": "前台服务"}, {"id": 280, "name": "fs-marketing-qywx", "remark": "营销通小程序&企业微信流量接入", "department": "深研营销业务部", "orgs": [], "level": "L1", "mainOwner": "郑辉Harry", "owners": ["郑辉Harry", "罗勇Pate", "张树锋Halcyon", "周浮洋bruce", "张玉佳Mike"], "category": "前台服务"}, {"id": 564, "name": "fs-warehouse-avatar", "remark": "文件服务：头像服务", "department": "平台运营架构组", "orgs": [], "level": "L2", "mainOwner": "安宜龙Andy", "owners": ["李锐colin", "安宜龙Andy", "吴志辉<PERSON>Z<PERSON>hui"], "category": ""}, {"id": 20, "name": "attachment-search-datamgr-cluster", "remark": "主站附件搜索数据调度服务", "department": "深研营销业务部", "orgs": [], "level": "", "mainOwner": "郑辉Harry", "owners": [], "category": ""}, {"id": 2359, "name": "config-service-handle", "remark": "", "department": "", "orgs": [], "level": "L1", "mainOwner": "", "owners": [], "category": ""}, {"id": 92, "name": "fs-bi-fio-server", "remark": "r2", "department": "数据业务技术BI组", "orgs": ["销售业务-BI"], "level": "L3", "mainOwner": "翟付杰Jeffrey", "owners": ["翟付杰Jeffrey"], "category": ""}, {"id": 2321, "name": "fs-crm-manufacturing", "remark": "制造行业对象相关的服务", "department": "制造行业开发组2", "orgs": [], "level": "L1", "mainOwner": "李秋林<PERSON>", "owners": ["李秋林<PERSON>", "郭明Zero", "谢向达", "戴云", "韦建韩Barry", "颜彩朵", "杨希luke", "丁成<PERSON><PERSON>"], "category": "前台服务"}, {"id": 367, "name": "fs-paas-function-service-debug", "remark": "函数forDebug", "department": "PaaS业务业务平台开发平台组", "orgs": ["销售业务-自定义对象", "销售协同-平台业务部"], "level": "L2", "mainOwner": "斯作益seth", "owners": ["斯作益seth", "刘一鸣Adam", "吴俊文Raymond"], "category": "前台服务"}, {"id": 633, "name": "msg-center", "remark": "企信消息转发-fxiaoke登录验证服务", "department": "平台运营架构组", "orgs": [], "level": "L3", "mainOwner": "吴志辉<PERSON>Z<PERSON>hui", "owners": ["谷广田", "吴志辉<PERSON>Z<PERSON>hui"], "category": ""}, {"id": 57, "name": "fs-apibus-ncrm", "remark": "内部CRM业务调用网关，承载所有CRM业务接口调用", "department": "PaaS业务业务平台部", "orgs": ["销售业务-自定义对象"], "level": "L0", "mainOwner": "郑磊<PERSON><PERSON><PERSON>", "owners": ["王毅Wang<PERSON>i", "郑磊<PERSON><PERSON><PERSON>", "赵琚"], "category": "前台服务"}, {"id": 76, "name": "fs-audit-log-biz", "remark": "审计日志", "department": "PaaS业务业务平台开发平台组", "orgs": ["销售协同-平台业务部"], "level": "L2", "mainOwner": "吴俊文Raymond", "owners": ["陈晓彬", "吴俊文Raymond", "张勇ZhangYong"], "category": "前台服务"}, {"id": 261, "name": "fs-kiscloud-provider", "remark": "KIS云对接provider", "department": "互联业务集成平台组", "orgs": [], "level": "L2", "mainOwner": "柯南颖", "owners": ["柯南颖"], "category": "后台异步服务"}, {"id": 2369, "name": "fs-paas-app-udobj-rest4realtime", "remark": "paas自定义对象接口", "department": "PaaS业务业务平台基础对象组", "orgs": ["销售业务-自定义对象"], "level": "L1", "mainOwner": "冯津<PERSON><PERSON><PERSON>", "owners": ["郑磊<PERSON><PERSON><PERSON>", "郑子阳Julian", "李磊Leo", "赵琚", "周伟荣", "王毅Wang<PERSON>i", "冯津<PERSON><PERSON><PERSON>"], "category": "前台服务"}, {"id": 2476, "name": "fs-auto-test", "remark": "自动化测试服务", "department": "互联业务互联平台组", "orgs": [], "level": "L3", "mainOwner": "钟兴ZhongXing", "owners": ["钟兴ZhongXing"], "category": "合并部署服务"}, {"id": 101, "name": "fs-bi-org", "remark": "BI-组织架构", "department": "数据业务技术BI组", "orgs": ["销售业务-BI"], "level": "L2", "mainOwner": "翟付杰Jeffrey", "owners": ["翟付杰Jeffrey", "陈江飞chenjiangfei", "郑子阳Julian"], "category": "前台服务"}, {"id": 128, "name": "fs-cas-overlay", "remark": "内部sso单点登录认证，使用纷享销客账户。负责人：李锐colin", "department": "平台运营架构组", "orgs": [], "level": "L3", "mainOwner": "吴志辉<PERSON>Z<PERSON>hui", "owners": [], "category": ""}, {"id": 521, "name": "fs-stone-backup-s3", "remark": "FastDFS文件备份服务", "department": "平台运营架构组", "orgs": [], "level": "L3", "mainOwner": "安宜龙Andy", "owners": ["安宜龙Andy", "刘云松samuel"], "category": ""}, {"id": 59, "name": "fs-approval-provider", "remark": "开平协同", "department": "互联业务互联平台组", "orgs": ["互联业务-开平对接组"], "level": "", "mainOwner": "钟兴ZhongXing", "owners": ["冯永亮Frank", "钟兴ZhongXing"], "category": ""}, {"id": 107, "name": "fs-bi-sqlengine", "remark": "负责人： 翟付杰,刘强,吴志辉", "department": "数据业务技术BI组", "orgs": [], "level": "L1", "mainOwner": "翟付杰Jeffrey", "owners": ["翟付杰Jeffrey", "陈江飞chenjiangfei", "郑子阳Julian"], "category": ""}, {"id": 2426, "name": "fs-mq-bus", "remark": "MQ消息环境分发", "department": "PaaS业务业务平台开发平台组", "orgs": [], "level": "L0", "mainOwner": "谷广田", "owners": ["谷广田", "吴志辉<PERSON>Z<PERSON>hui"], "category": "后台异步服务"}, {"id": 2441, "name": "fs-process-service-biz-set-for-cloud", "remark": "专为专属云合成的 流程服务集合 ; 业务层 1. 业务流程 2. 审批流程 3. 公共服务 4. 阶段推进器 5. 工作流程", "department": "PaaS业务基础平台流程组", "orgs": [], "level": "", "mainOwner": "万松<PERSON>", "owners": ["万松<PERSON>"], "category": ""}, {"id": 2518, "name": "egress-proxy-management", "remark": "egress-proxy-service内部管理系统跨云通道，单开分区，底层是egress-proxy-service", "department": "平台运营架构组", "orgs": [], "level": "L2", "mainOwner": "刘全胜liuquansheng", "owners": ["刘全胜liuquansheng", "吴志辉<PERSON>Z<PERSON>hui"], "category": "前台服务"}, {"id": 2460, "name": "fs-organization-adapter-build-expansion", "remark": "--", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "", "mainOwner": "刘文绪dio", "owners": ["刘文绪dio", "尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "category": ""}, {"id": 567, "name": "fs-warehouse-cross", "remark": "文件系统－企业互联文件存储服务", "department": "平台运营架构组", "orgs": [], "level": "L1", "mainOwner": "安宜龙Andy", "owners": ["安宜龙Andy"], "category": "前台服务"}, {"id": 646, "name": "paas-pg-scanner", "remark": "扫描paas数据库的变更记录发送MQ", "department": "北研业务线", "orgs": ["平台运营-PaaS"], "level": "L0", "mainOwner": "谷广田", "owners": ["谷广田", "李锐colin", "李磊Leo", "郑子阳Julian"], "category": ""}, {"id": 310, "name": "fs-oms-provider", "remark": "企信在线状态服务", "department": "PaaS业务业务平台协同业务组", "orgs": [], "level": "L1", "mainOwner": "梁岩超Charles", "owners": ["尚壬鹏<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>硕<PERSON><PERSON>", "梁岩超Charles"], "category": "前台服务"}, {"id": 374, "name": "fs-paas-license-surrogate", "remark": "专属云license代理", "department": "PaaS业务基础平台元数据组", "orgs": [], "level": "L0", "mainOwner": "李晨Jason", "owners": ["杨华国YHG", "钱凌锋irony", "吕杰lvjie", "李晨Jason", "梁梓闻liang<PERSON><PERSON>", "李磊Leo", "郑子阳Julian"], "category": "前台服务"}, {"id": 2417, "name": "sync-data-mq-provider", "remark": "数据同步mq模块", "department": "互联业务互联平台组", "orgs": ["互联业务-互联平台组"], "level": "L2", "mainOwner": "刘诗林liushilin", "owners": ["刘诗林liushilin", "张诚fine", "曾令文Evan", "钟兴ZhongXing"], "category": ""}, {"id": 33, "name": "cloud-mq-consumer", "remark": "跨云mq消息投递，消息消费端，用于消费需要投递的mq消息，把消息发送给cloud-mq-proxy,如果此服务异常，会导致待投递的mq失败，例如配置中心数据，纷享到专属；license变更投递等", "department": "平台运营架构组", "orgs": [], "level": "L2", "mainOwner": "侯世鹏", "owners": ["侯世鹏"], "category": "后台异步服务"}, {"id": 2425, "name": "egress-api-service", "remark": "访问第三方服务的代理", "department": "平台运营架构组", "orgs": [], "level": "L0", "mainOwner": "刘全胜liuquansheng", "owners": ["刘全胜liuquansheng", "李锐colin"], "category": "前台服务"}, {"id": 88, "name": "fs-bi-custom-statistic-schedule", "remark": "物化视图刷新服务", "department": "数据业务技术平台组", "orgs": [], "level": "L2", "mainOwner": "纪二飞Roy", "owners": ["纪二飞Roy"], "category": ""}, {"id": 133, "name": "fs-checkins-biz", "remark": "外勤统计服务", "department": "消费品业务技术外勤组", "orgs": ["快销农牧行业组"], "level": "L2", "mainOwner": "张世民", "owners": ["贺政忠HeZhengzhong", "何静媛hejy", "郭思卿", "安丰胜Richard", "张世民"], "category": ""}]}