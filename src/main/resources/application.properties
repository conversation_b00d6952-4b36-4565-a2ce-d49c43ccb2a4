spring.application.name=fs-idp-helper
spring.config.import=optional:cms:${spring.application.name}
fxiaoke.starter.config.cms.key=ajotb40mdlyksh36
server.error.whitelabel.enabled=false
# oauth2-proxy token header larger than 16KB
server.max-http-request-header-size=1MB
# global json config
spring.jackson.time-zone=GMT+8
spring.jackson.locale=zh_CN
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.default-property-inclusion=NON_NULL
spring.jackson.serialization.write-empty-json-arrays=false
# api-docs
springdoc.api-docs.path=/api-docs
springdoc.enable-spring-security=false
springdoc.api-docs.enabled=false
springdoc.swagger-ui.enabled=false
fxiaoke.starter.config.keycloak.security.permit-all=/headers,/public/**,/api-docs/**,/swagger-ui/**,/api-docs.yaml
# idp helper config
sharecrm.api.idp.helper.update-user-enabled=false
sharecrm.api.idp.helper.k8s-app-url=https://k8s-app.foneshare.cn
sharecrm.api.idp.helper.config-token=ENC(B908E23AF05D7C8DAF54F8ACC60D6CBCAC63F879256025881DF229C40C9B5065EE04D9EACEE7D0D00C333A9CA3F2E5EEE102D8B33BBA9A4EE1274B710F2548E1)
# cache
spring.cache.type=caffeine
spring.cache.caffeine.spec=maximumSize=1000,expireAfterWrite=30m

# \u76FE\u5C71\u5E94\u7528\u914D\u7F6E\uFF0Ckey \u662F\u5E94\u7528\u540D\uFF0Cvalue \u662F\u76FE\u5C71tunnel\u914D\u7F6E\u5217\u8868
sharecrm.api.idp.helper.dun-shan-app-configs.egress-proxy-mq-proxy=egress-proxy-mq-proxy
sharecrm.api.idp.helper.dun-shan-app-configs.egress-proxy-datax=egress-proxy-datax
sharecrm.api.idp.helper.dun-shan-app-configs.egress-proxy-management=egress-proxy-management
sharecrm.api.idp.helper.dun-shan-app-configs.egress-proxy-service=egress-proxy-tunnel

# \u7EB7\u4EAB\u4E91
sharecrm.api.idp.helper.clouds.foneshare.id=foneshare
sharecrm.api.idp.helper.clouds.foneshare.name=\u7EB7\u4EAB\u4E91
sharecrm.api.idp.helper.clouds.foneshare.tunnel=dunshan.fxiaoke.com
sharecrm.api.idp.helper.clouds.foneshare.namespaces=foneshare,foneshare-vip,foneshare-svip,foneshare-urgent
# \u534E\u4E3A\u4E91\u5317\u4EAC\u56DB
sharecrm.api.idp.helper.clouds.huaweicloud.id=k8s2
sharecrm.api.idp.helper.clouds.huaweicloud.name=\u534E\u4E3A\u4E91\u5317\u4EAC\u56DB
sharecrm.api.idp.helper.clouds.huaweicloud.tunnel=hwcloud.fxiaoke.com
sharecrm.api.idp.helper.clouds.huaweicloud.namespaces=huaweicloud-public-prod
# \u963F\u91CC\u4E91\u676D\u5DDE
sharecrm.api.idp.helper.clouds.aliyun.id=ale-k8s1
sharecrm.api.idp.helper.clouds.aliyun.name=\u963F\u91CC\u4E91\u676D\u5DDE
sharecrm.api.idp.helper.clouds.aliyun.tunnel=ale.fxiaoke.com
sharecrm.api.idp.helper.clouds.aliyun.namespaces=ale-public-prod
# \u4E9A\u9A6C\u900A\u6B27\u6D32\u6CD5\u5170\u514B\u798F
sharecrm.api.idp.helper.clouds.aws-eu.id=hws-k8s1
sharecrm.api.idp.helper.clouds.aws-eu.name=\u4E9A\u9A6C\u900A\u6B27\u6D32\u6CD5\u5170\u514B\u798F
sharecrm.api.idp.helper.clouds.aws-eu.tunnel=eu.sharecrm.com
sharecrm.api.idp.helper.clouds.aws-eu.namespaces=hws-public-prod
# \u4E9A\u9A6C\u900A\u4E1C\u4E9A\u9999\u6E2F
sharecrm.api.idp.helper.clouds.aws-hk.id=ksc-k8s1
sharecrm.api.idp.helper.clouds.aws-hk.name=\u4E9A\u9A6C\u900A\u4E1C\u4E9A\u9999\u6E2F
sharecrm.api.idp.helper.clouds.aws-hk.tunnel=hk.sharecrm.com
sharecrm.api.idp.helper.clouds.aws-hk.namespaces=ksc-ksc-prod
# \u4E9A\u9A6C\u900A\u4E1C\u5357\u4E9A\u65B0\u52A0\u5761
sharecrm.api.idp.helper.clouds.aws-sg.id=forsharecrm-k8s1
sharecrm.api.idp.helper.clouds.aws-sg.name=\u4E9A\u9A6C\u900A\u4E1C\u5357\u4E9A\u65B0\u52A0\u5761
sharecrm.api.idp.helper.clouds.aws-sg.tunnel=sea.sharecrm.com
sharecrm.api.idp.helper.clouds.aws-sg.namespaces=forsharecrm-public-prod
# \u4E9A\u9A6C\u900A\u5317\u7F8E\u5317\u52A0\u5DDE
sharecrm.api.idp.helper.clouds.aws-us.id=kemaicrm-k8s1
sharecrm.api.idp.helper.clouds.aws-us.name=\u4E9A\u9A6C\u900A\u5317\u7F8E\u5317\u52A0\u5DDE
sharecrm.api.idp.helper.clouds.aws-us.tunnel=na.sharecrm.com
sharecrm.api.idp.helper.clouds.aws-us.namespaces=kemaicrm-public-prod
# \u53CC\u80DE\u80CE\u4E13\u5C5E\u4E91
sharecrm.api.idp.helper.clouds.sbt.id=sbt-k8s1
sharecrm.api.idp.helper.clouds.sbt.name=\u53CC\u80DE\u80CE
sharecrm.api.idp.helper.clouds.sbt.tunnel=crm.sbtjt.com
sharecrm.api.idp.helper.clouds.sbt.namespaces=huaweicloud-sbt-prod
# \u7D2B\u5149\u4E91
sharecrm.api.idp.helper.clouds.ucd.id=ucd-k8s2
sharecrm.api.idp.helper.clouds.ucd.name=\u7D2B\u5149\u4E91
sharecrm.api.idp.helper.clouds.ucd.tunnel=crm.unicloudea.com
sharecrm.api.idp.helper.clouds.ucd.namespaces=ucd-public-prod
# \u8BB8\u7EE7\u4E13\u5C5E\u4E91
sharecrm.api.idp.helper.clouds.xjgc.id=xjgc-k8s1
sharecrm.api.idp.helper.clouds.xjgc.name=\u8BB8\u7EE7
sharecrm.api.idp.helper.clouds.xjgc.tunnel=crm.xjgc.com
sharecrm.api.idp.helper.clouds.xjgc.namespaces=xjgc-public-prod
# \u6D77\u4FE1\u4E13\u5C5E\u4E91
sharecrm.api.idp.helper.clouds.hisense.id=hisense-k8s1
sharecrm.api.idp.helper.clouds.hisense.name=\u6D77\u4FE1
sharecrm.api.idp.helper.clouds.hisense.tunnel=xiaoke.hisense.com
sharecrm.api.idp.helper.clouds.hisense.namespaces=hisense-public-prod
# \u94C1\u5854\u4E13\u5C5E\u4E91
sharecrm.api.idp.helper.clouds.chinatower.id=chinatower-k8s1
sharecrm.api.idp.helper.clouds.chinatower.name=\u94C1\u5854
sharecrm.api.idp.helper.clouds.chinatower.tunnel=zlbss-crm.chinatowercom.cn
sharecrm.api.idp.helper.clouds.chinatower.namespaces=chinatower-public-prod
# \u8499\u725B\u4E13\u5C5E\u4E91
sharecrm.api.idp.helper.clouds.mengniu.id=mengniu-k8s1
sharecrm.api.idp.helper.clouds.mengniu.name=\u8499\u725B
sharecrm.api.idp.helper.clouds.mengniu.tunnel=msv.mengniu.cn
sharecrm.api.idp.helper.clouds.mengniu.namespaces=mengniu-public-prod
# \u4F55\u6C0F\u773C\u79D1\u4E13\u5C5E\u4E91
sharecrm.api.idp.helper.clouds.hsyk.id=hsyk-k8s1
sharecrm.api.idp.helper.clouds.hsyk.name=\u4F55\u6C0F\u773C\u79D1
sharecrm.api.idp.helper.clouds.hsyk.tunnel=crm.hevision.com
sharecrm.api.idp.helper.clouds.hsyk.namespaces=hsyk-public-prod
# \u4F0D\u5B50\u9189\u4E13\u5C5E\u4E91
sharecrm.api.idp.helper.clouds.wuzizui99.id=wuzizui99-k8s1
sharecrm.api.idp.helper.clouds.wuzizui99.name=\u4F0D\u5B50\u9189
sharecrm.api.idp.helper.clouds.wuzizui99.tunnel=crm.hnwzz.cn
sharecrm.api.idp.helper.clouds.wuzizui99.namespaces=wuzizui99-public-prod
# \u79D1\u5927\u8BAF\u98DE\u4E13\u5C5E\u4E91
sharecrm.api.idp.helper.clouds.iflytek.id=iflytek-k8s1
sharecrm.api.idp.helper.clouds.iflytek.name=\u79D1\u5927\u8BAF\u98DE
sharecrm.api.idp.helper.clouds.iflytek.tunnel=crm.iflytek.com
sharecrm.api.idp.helper.clouds.iflytek.namespaces=iflytek-public-prod
# \u6D77\u514B\u65AF\u5EB7\u4E13\u5C5E\u4E91
sharecrm.api.idp.helper.clouds.hexagonmi.id=hexagonmi-k8s1
sharecrm.api.idp.helper.clouds.hexagonmi.name=\u6D77\u514B\u65AF\u5EB7
sharecrm.api.idp.helper.clouds.hexagonmi.tunnel=crm.hexagonmi.com.cn
sharecrm.api.idp.helper.clouds.hexagonmi.namespaces=hexagonmi-public-prod
# \u626C\u519C\u5316\u5DE5\u4E13\u5C5E\u4E91
sharecrm.api.idp.helper.clouds.yangnongchem.id=yangnongchem-k8s1
sharecrm.api.idp.helper.clouds.yangnongchem.name=\u626C\u519C\u5316\u5DE5
sharecrm.api.idp.helper.clouds.yangnongchem.tunnel=crm.yangnong.cn
sharecrm.api.idp.helper.clouds.yangnongchem.namespaces=yangnongchem-public-prod
# \u7535\u4FE1\u667A\u63A7\u4E13\u5C5E\u4E91
sharecrm.api.idp.helper.clouds.teleagi.id=teleagi-k8s1
sharecrm.api.idp.helper.clouds.teleagi.name=\u7535\u4FE1\u667A\u63A7
sharecrm.api.idp.helper.clouds.teleagi.tunnel=crm.teleagi.cn
sharecrm.api.idp.helper.clouds.teleagi.namespaces=teleagi-public-prod
# \u4E2D\u8239\u5B81\u590F\u4E13\u5C5E\u4E91
sharecrm.api.idp.helper.clouds.cpgc.id=cpgc-k8s1
sharecrm.api.idp.helper.clouds.cpgc.name=\u4E2D\u8239\u5B81\u590F
sharecrm.api.idp.helper.clouds.cpgc.tunnel=globalservice.cpgc.net.cn
sharecrm.api.idp.helper.clouds.cpgc.namespaces=cpgc-public-prod
# \u4E2D\u8239\u6CD5\u5170\u514B\u798F\u4E13\u5C5E\u4E91
sharecrm.api.idp.helper.clouds.wingd.id=wingd-k8s1
sharecrm.api.idp.helper.clouds.wingd.name=\u4E2D\u8239\u6CD5\u5170\u514B\u798F
sharecrm.api.idp.helper.clouds.wingd.tunnel=globalservice.wingd.com
sharecrm.api.idp.helper.clouds.wingd.namespaces=wingd-public-prod
# \u79D1\u534E\u4E13\u5C5E\u4E91
sharecrm.api.idp.helper.clouds.kehua.id=kehua-k8s1
sharecrm.api.idp.helper.clouds.kehua.name=\u79D1\u534E
sharecrm.api.idp.helper.clouds.kehua.tunnel=crm.kehua.com
sharecrm.api.idp.helper.clouds.kehua.namespaces=kehua-public-prod
# \u4EAC\u535A\u4E13\u5C5E\u4E91
sharecrm.api.idp.helper.clouds.jingbo.id=jingbo-k8s1
sharecrm.api.idp.helper.clouds.jingbo.name=\u4EAC\u535A
sharecrm.api.idp.helper.clouds.jingbo.tunnel=ncrm.chambroad.com
sharecrm.api.idp.helper.clouds.jingbo.namespaces=jingbo-public-prod
# \u7279\u53D8\u4E13\u5C5E\u4E91
sharecrm.api.idp.helper.clouds.tbea.id=tbea-k8s1
sharecrm.api.idp.helper.clouds.tbea.name=\u7279\u53D8
sharecrm.api.idp.helper.clouds.tbea.tunnel=icrm.tbea.com
sharecrm.api.idp.helper.clouds.tbea.namespaces=tbea-public-prod
sharecrm.api.idp.helper.clouds.tbea-vm.id=tbea-vm
sharecrm.api.idp.helper.clouds.tbea-vm.name=\u7279\u53D8
sharecrm.api.idp.helper.clouds.tbea-vm.tunnel=60.30.33.36:8180
sharecrm.api.idp.helper.clouds.tbea-vm.namespaces=tbea-public-prod
# \u6A21\u677F\u4E91
sharecrm.api.idp.helper.clouds.cloudmodel.id=cloudmodel-k8s1
sharecrm.api.idp.helper.clouds.cloudmodel.name=IDC\u4E4C\u5170\u5BDF\u5E03-\u6A21\u677F\u4E91
sharecrm.api.idp.helper.clouds.cloudmodel.tunnel=www.fxiaokeadmin.com
sharecrm.api.idp.helper.clouds.cloudmodel.namespaces=cloudmodel-public-prod
sharecrm.api.idp.helper.clouds.cloudmodel-vm.id=cloudmodel-vm
sharecrm.api.idp.helper.clouds.cloudmodel-vm.name=IDC\u4E4C\u5170\u5BDF\u5E03-\u6A21\u677F\u4E91
sharecrm.api.idp.helper.clouds.cloudmodel-vm.tunnel=63.176.14.139:8090
sharecrm.api.idp.helper.clouds.cloudmodel-vm.namespaces=cloudmodel-public-prod
# \u79C1\u6709\u4E91
sharecrm.api.idp.helper.clouds.forceecrm.id=forceecrm-k8s1
sharecrm.api.idp.helper.clouds.forceecrm.name=IDC\u4E4C\u5170\u5BDF\u5E03-\u79C1\u6709\u4E91
sharecrm.api.idp.helper.clouds.forceecrm.tunnel=www.forceecrm.com
sharecrm.api.idp.helper.clouds.forceecrm.namespaces=forceecrm-public-prod
# \u6027\u80FD\u4E91
sharecrm.api.idp.helper.clouds.ucd-test.id=ucd-k8s1
sharecrm.api.idp.helper.clouds.ucd-test.name=IDC\u4E4C\u5170\u5BDF\u5E03-\u6027\u80FD\u4E91
sharecrm.api.idp.helper.clouds.ucd-test.tunnel=ucdtest.fxiaoke.com
sharecrm.api.idp.helper.clouds.ucd-test.namespaces=ucd-public-test
# ALLINK8S
sharecrm.api.idp.helper.clouds.allink8s.id=allink8s-k8s1
sharecrm.api.idp.helper.clouds.allink8s.name=IDC\u4E4C\u5170\u5BDF\u5E03-ALLINK8S
sharecrm.api.idp.helper.clouds.allink8s.tunnel=www.allink8s.com
sharecrm.api.idp.helper.clouds.allink8s.namespaces=allink8s-public-prod
