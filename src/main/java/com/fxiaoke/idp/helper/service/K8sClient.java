package com.fxiaoke.idp.helper.service;

import com.fxiaoke.idp.helper.model.K8sApiResponse;
import com.fxiaoke.idp.helper.model.K8sAppAddress;
import com.fxiaoke.idp.helper.model.K8sDeployment;
import feign.Headers;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 纷享K8S管理平台接口客户端
 */
@Component
@FeignClient(value = "k8sClient", url = "${sharecrm.api.idp.helper.k8s-app-url:https://k8s-app.foneshare.cn}")
public interface K8sClient {

  /**
   * 根据应用名称查询应用地址信息
   *
   * @param app 应用名称
   * @return 应用地址信息
   */
  @GetMapping(value = "/api/openapi/app/address")
  @Headers({ "Content-Type: application/json;charset=UTF-8" })
  K8sApiResponse<K8sAppAddress> getAppAddress(@RequestParam String app);

  /**
   * 查询部署列表
   *
   * @param cluster   集群名称
   * @param namespace 命名空间
   * @return 部署列表信息
   */
  @GetMapping(value = "/api/openapi/deployment/list")
  @Headers({ "Content-Type: application/json;charset=UTF-8" })
  K8sApiResponse<K8sDeployment> getDeploymentList(@RequestParam String cluster, @RequestParam String namespace);
}
