package com.fxiaoke.idp.helper.service;

import com.facishare.organization.api.model.department.DepartmentDto;
import com.facishare.organization.api.model.department.arg.GetDepartmentDtoArg;
import com.facishare.organization.api.model.department.result.GetDepartmentDtoResult;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.employee.arg.GetEmployeeDtoArg;
import com.facishare.organization.api.model.employee.arg.GetEmployeesDtoByNameArg;
import com.fxiaoke.boot.autoconfigure.iam.model.UserDetail;
import com.fxiaoke.common.crypto.HexHash;
import com.fxiaoke.idp.helper.model.FsVerifyPasswordArg;
import com.fxiaoke.idp.helper.model.FsVerifyPasswordResult;
import com.fxiaoke.idp.helper.model.FsVerifyPasswordResultData;
import com.fxiaoke.idp.helper.model.UserPhoneValidateRequest;
import com.fxiaoke.idp.helper.utils.EmptyExpiry;
import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.HashSet;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * 纷享自己的用户、组织管理接口
 */
@Slf4j
@Service
@AllArgsConstructor
public class FsUserService {

  public static final String FS_ENTERPRISE_ACCOUNT = "fs";
  public static final int FS_ENTERPRISE_ID = 1;
  private final FsLoginClient fsLoginClient;
  private final FsOrgClient fsOrgClient;

  private final AsyncLoadingCache<String, UserDetail> cache = Caffeine.newBuilder()
    .expireAfter(new EmptyExpiry<String, UserDetail>(Duration.ofSeconds(1),
      Duration.ofHours(8),
      e -> Objects.isNull(e) || Objects.isNull(e.getEmail())))
    .maximumSize(1000)
    .buildAsync(this::queryUser);

  private final AsyncLoadingCache<Integer, DepartmentDto> departmentCache = Caffeine.newBuilder()
    .expireAfter(new EmptyExpiry<Integer, DepartmentDto>(Duration.ofSeconds(1),
      Duration.ofHours(8),
      e -> Objects.isNull(e) || Objects.isNull(e.getName())))
    .maximumSize(1000)
    .buildAsync(this::queryRemoteDepartment);


  public UserDetail validateByPhone(UserPhoneValidateRequest request) {
    try {
      FsVerifyPasswordArg arg = new FsVerifyPasswordArg();
      arg.setEnterpriseAccount(FS_ENTERPRISE_ACCOUNT);
      arg.setEmployeeAccount(request.getPhone());
      arg.setPassword(HexHash.md5(request.getPassword()).toLowerCase());
      FsVerifyPasswordResult result = fsLoginClient.verifyUserAccount(arg);
      log.info("fs user login client verify result:{}", result);
      FsVerifyPasswordResultData data = result.getVerifyEmployeePasswordData();
      if (Objects.isNull(data) || data.getErrorNumber() != 0) {
        return null;
      }
      int employeeId = data.getEmployeeEditionDataWithProperties().getEmployeeId();
      return queryUserByEmployeeId("" + employeeId);
    } catch (Exception e) {
      log.warn("fs user login failed", e);
      return null;
    }
  }

  public UserDetail queryUserByEmployeeId(String employeeId) {
    try {
      return cache.get(employeeId).get();
    } catch (InterruptedException e) {
      log.warn("fs user query failed", e);
      Thread.currentThread().interrupt();
      return null;
    } catch (ExecutionException e) {
      log.warn("fs user query failed", e);
      return null;
    }
  }

  public UserDetail queryUserByName(String name) {
    GetEmployeesDtoByNameArg arg = new GetEmployeesDtoByNameArg();
    arg.setEnterpriseId(1);
    arg.setName(name);
    EmployeeDto employee = fsOrgClient.getEmployeesByName(arg).getEmployeeDto();
    return employeeToUserDetail(employee);
  }

  private UserDetail employeeToUserDetail(EmployeeDto employee) {
    UserDetail userDetail = new UserDetail();
    userDetail.setEmployeeId(employee.getEmployeeId() + "");
    userDetail.setUsername(employee.getName());
    userDetail.setDisplayName(employee.getFullName());
    userDetail.setPhone(employee.getMobile());
    userDetail.setEmail(employee.getEmail());
    //职位
    userDetail.setPost(employee.getPost());

    UserDetail leader = new UserDetail();
    leader.setEmployeeId("" + employee.getLeaderId());
    userDetail.setLeader(leader);

    Set<Integer> departments = new HashSet<>();
    Optional.ofNullable(employee.getMainDepartmentId()).ifPresent(departments::add);
    Optional.ofNullable(employee.getOtherDepartmentIds()).ifPresent(departments::addAll);
    userDetail.setDepartment(joinDepartment(departments));
    userDetail.setDepartmentIds(departments.stream().map(String::valueOf)
      .collect(Collectors.joining(",")));
    return userDetail;
  }

  public DepartmentDto queryDepartmentById(Integer id) {
    try {
      return departmentCache.get(id).get();
    } catch (InterruptedException e) {
      log.warn("fs department query failed", e);
      Thread.currentThread().interrupt();
      return null;
    } catch (ExecutionException e) {
      log.warn("fs department query failed", e);
      return null;
    }
  }

  private UserDetail queryUser(String employeeId) {
    try {
      GetEmployeeDtoArg arg = new GetEmployeeDtoArg();
      arg.setEmployeeId(Integer.parseInt(employeeId));
      arg.setEnterpriseId(FS_ENTERPRISE_ID);
      EmployeeDto employee = fsOrgClient.getEmployeeDto(arg).getEmployeeDto();
      log.info("fs user detail:{}", employee);
      return employeeToUserDetail(employee);
    } catch (Exception e) {
      log.warn("fs user query failed", e);
      return null;
    }
  }

  private String joinDepartment(Set<Integer> ids) {
    return ids.stream().map(this::queryDepartmentById)
      .map(DepartmentDto::getName)
      .collect(Collectors.joining(","));
  }

  private DepartmentDto queryRemoteDepartment(Integer id) {
    GetDepartmentDtoArg arg = new GetDepartmentDtoArg();
    arg.setDepartmentId(id);
    arg.setEnterpriseId(FS_ENTERPRISE_ID);
    GetDepartmentDtoResult rs = fsOrgClient.getDepartmentDto(arg);
    return rs.getDepartment();
  }

}
