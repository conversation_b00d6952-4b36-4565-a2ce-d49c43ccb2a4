package com.fxiaoke.idp.helper.service;

import com.fxiaoke.idp.helper.config.HelperProperties;
import com.fxiaoke.idp.helper.model.AppReportBizLog;
import com.fxiaoke.idp.helper.model.K8sApiResponse;
import com.fxiaoke.idp.helper.model.K8sApp;
import com.fxiaoke.idp.helper.model.K8sAppAddress;
import com.fxiaoke.idp.helper.model.K8sDeployment;
import com.fxiaoke.idp.helper.utils.Constants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.function.Predicate;

/**
 * 纷享K8S管理平台接口
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class K8sService {

  private final K8sClient k8sClient;

  private final HelperProperties properties;

  /**
   * 查询应用列表
   *
   * @return 应用列表信息
   */
  public List<K8sApp> queryAppList() {
    try {
      K8sApiResponse<K8sApp> rs = k8sClient.getAppList();
      return Objects.requireNonNullElse(rs.getData(), List.of());
    } catch (Exception e) {
      log.warn("query k8s app list failed", e);
      return List.of();
    }
  }

  /**
   * 根据应用名称查询应用地址信息
   *
   * @param app 应用名称
   * @return 应用地址信息
   */
  public List<K8sAppAddress> queryAppAddress(String app) {
    try {
      K8sApiResponse<K8sAppAddress> rs = k8sClient.getAppAddress(app);
      log.info("query app address result, app: {}, message: {}", app, rs.getMessage());
      return Objects.requireNonNullElse(rs.getData(), List.of());
    } catch (Exception e) {
      log.warn("query k8s app address failed, app: {}", app, e);
      return List.of();
    }
  }

  /**
   * 查询部署列表
   *
   * @param cluster   集群名称
   * @param namespace 命名空间
   * @return 部署列表信息
   */
  public List<K8sDeployment> queryDeploymentList(String cluster, String namespace) {
    try {
      K8sApiResponse<K8sDeployment> rs = k8sClient.getDeploymentList(cluster, namespace);
      return Objects.requireNonNullElse(rs.getData(), List.of());
    } catch (Exception e) {
      log.warn("query k8s deployment list failed, cluster: {}, namespace: {}", cluster, namespace, e);
      return List.of();
    }
  }

  public void doReport() {
    List<K8sApp> apps = queryAppList();
    if (CollectionUtils.isEmpty(apps)) {
      log.warn("query k8s app list failed, skip task");
      return;
    }
    properties.getAppReportClusters()
      .forEach((cluster, namespaces) -> namespaces.forEach(namespace -> {
        List<K8sDeployment> deployments = queryDeploymentList(cluster, namespace);
        saveDeploymentBizLog(apps, deployments);
      }));
  }

  private void saveDeploymentBizLog(List<K8sApp> apps, List<K8sDeployment> deployments) {
    if (CollectionUtils.isEmpty(deployments)) {
      log.warn("query k8s deployment list failed, skip task");
      return;
    }
    deployments.forEach(new Consumer<K8sDeployment>() {
      @Override
      public void accept(K8sDeployment deploy) {
        AppReportBizLog biz = new AppReportBizLog();
        biz.setEa("fs");
        biz.setTenantId("1");
        biz.setApp("reporter");
        biz.setProfile(Constants.PROFILE);
        biz.setServerIp(Constants.IP);

        biz.setCluster(deploy.getCluster());
        biz.setNamespace(deploy.getNamespace());

        apps.stream().filter(new Predicate<K8sApp>() {
            @Override
            public boolean test(K8sApp app) {
              return app.getName().equals(deploy.getName());
            }
          }).findFirst()
          .ifPresent(new Consumer<K8sApp>() {
            @Override
            public void accept(K8sApp k8sApp) {

            }
          });
      }
    });


  }

}
