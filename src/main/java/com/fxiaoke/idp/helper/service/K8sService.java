package com.fxiaoke.idp.helper.service;

import com.fxiaoke.idp.helper.model.K8sAppAddress;
import com.fxiaoke.idp.helper.model.K8sAppAddressResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 纷享K8S管理平台接口
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class K8sService {

  private final K8sClient k8sClient;

  /**
   * 根据应用名称查询应用地址信息
   *
   * @param app 应用名称
   * @return 应用地址信息
   */
  public List<K8sAppAddress> queryAppAddress(String app) {
    try {
      K8sAppAddressResponse rs = k8sClient.getAppAddress(app);
      log.info("query app address result, app: {}, message: {}", app, rs.getMessage());
      return Objects.requireNonNullElse(rs.getData(), List.of());
    } catch (Exception e) {
      log.warn("query k8s app address failed, app: {}", app, e);
      return List.of();
    }
  }
}
