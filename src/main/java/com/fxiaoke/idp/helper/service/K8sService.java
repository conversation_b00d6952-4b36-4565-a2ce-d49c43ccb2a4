package com.fxiaoke.idp.helper.service;

import com.fxiaoke.idp.helper.config.HelperProperties;
import com.fxiaoke.idp.helper.model.AppReportBizLog;
import com.fxiaoke.idp.helper.model.K8sApiResponse;
import com.fxiaoke.idp.helper.model.K8sApp;
import com.fxiaoke.idp.helper.model.K8sAppAddress;
import com.fxiaoke.idp.helper.model.K8sDeployment;
import com.fxiaoke.idp.helper.utils.Constants;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.ps.ProtostuffUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 纷享K8S管理平台接口
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class K8sService {

  private final K8sClient k8sClient;

  private final HelperProperties properties;

  /**
   * 查询应用列表
   *
   * @return 应用列表信息
   */
  public List<K8sApp> queryAppList() {
    try {
      K8sApiResponse<K8sApp> rs = k8sClient.getAppList();
      return Objects.requireNonNullElse(rs.getData(), List.of());
    } catch (Exception e) {
      log.warn("query k8s app list failed", e);
      return List.of();
    }
  }

  /**
   * 根据应用名称查询应用地址信息
   *
   * @param app 应用名称
   * @return 应用地址信息
   */
  public List<K8sAppAddress> queryAppAddress(String app) {
    try {
      K8sApiResponse<K8sAppAddress> rs = k8sClient.getAppAddress(app);
      log.info("query app address result, app: {}, message: {}", app, rs.getMessage());
      return Objects.requireNonNullElse(rs.getData(), List.of());
    } catch (Exception e) {
      log.warn("query k8s app address failed, app: {}", app, e);
      return List.of();
    }
  }

  /**
   * 查询部署列表
   *
   * @param cluster   集群名称
   * @param namespace 命名空间
   * @return 部署列表信息
   */
  public List<K8sDeployment> queryDeploymentList(String cluster, String namespace) {
    try {
      K8sApiResponse<K8sDeployment> rs = k8sClient.getDeploymentList(cluster, namespace);
      return Objects.requireNonNullElse(rs.getData(), List.of());
    } catch (Exception e) {
      log.warn("query k8s deployment list failed, cluster: {}, namespace: {}", cluster, namespace, e);
      return List.of();
    }
  }

  public void doReport() {
    List<K8sApp> apps = queryAppList();
    if (CollectionUtils.isEmpty(apps)) {
      log.warn("query k8s app list failed, skip task");
      return;
    }
    properties.getAppReportClusters()
        .forEach((cluster, namespaces) -> namespaces.forEach(namespace -> {
          List<K8sDeployment> deployments = queryDeploymentList(cluster, namespace);
          saveDeploymentBizLog(apps, deployments);
        }));
  }

  private void saveDeploymentBizLog(List<K8sApp> apps, List<K8sDeployment> deployments) {
    if (CollectionUtils.isEmpty(deployments)) {
      log.warn("query k8s deployment list failed, skip task");
      return;
    }
    deployments.forEach(deploy -> {
      AppReportBizLog biz = new AppReportBizLog();
      // 设置基础信息
      biz.setEa("fs");
      biz.setTenantId("1");
      biz.setApp("reporter");
      biz.setProfile(Constants.PROFILE);
      biz.setServerIp(Constants.IP);
      biz.setStamp(System.currentTimeMillis());

      // 设置部署信息
      biz.setCluster(deploy.getCluster());
      biz.setNamespace(deploy.getNamespace());
      biz.setPipelineReplicas(deploy.getPipelineReplicas());
      biz.setReplicas(deploy.getReplicas());

      // 解析并设置Java版本和Tomcat版本
      parseAndSetVersions(biz, deploy.getContainer0Image());

      apps.stream()
          .filter(app -> app.getName().equals(deploy.getName()))
          .findFirst()
          .ifPresent(k8sApp -> {
            // 设置应用相关信息
            biz.setName(k8sApp.getName());
            biz.setDepartment(k8sApp.getDepartment());
            biz.setLevel(k8sApp.getLevel());
            biz.setMainOwner(k8sApp.getMainOwner());
            // TODO: 需要获取部门leader信息，暂时留空
            // biz.setLeader(getLeaderByDepartment(k8sApp.getDepartment()));
          });
      // 发送BizLog
      BizLogClient.send("generic-biz-log", ProtostuffUtil.serializer(biz));
    });
  }

  /**
   * 解析container0Image并设置Java版本和Tomcat版本
   *
   * @param biz             AppReportBizLog对象
   * @param container0Image 容器镜像字符串
   */
  private void parseAndSetVersions(AppReportBizLog biz, String container0Image) {
    if (container0Image == null || container0Image.isEmpty()) {
      return;
    }

    try {
      // 解析Tomcat版本
      String tomcatVersion = parseTomcatVersion(container0Image);
      biz.setTomcatVersion(tomcatVersion);

      // 解析Java版本
      String javaVersion = parseJavaVersion(container0Image);
      biz.setJavaVersion(javaVersion);

      log.debug("Parsed versions from image {}: tomcat={}, java={}", container0Image, tomcatVersion, javaVersion);
    } catch (Exception e) {
      log.warn("Failed to parse versions from container image: {}", container0Image, e);
    }
  }

  /**
   * 从容器镜像字符串中解析Tomcat版本
   * 例如: fs-tomcat8, fs-tomcat9, fs-tomcat10
   */
  private String parseTomcatVersion(String container0Image) {
    if (container0Image.contains("fs-tomcat")) {
      // 匹配 fs-tomcat8, fs-tomcat9, fs-tomcat10 等
      if (container0Image.contains("fs-tomcat10")) {
        return "10";
      } else if (container0Image.contains("fs-tomcat9")) {
        return "9";
      } else if (container0Image.contains("fs-tomcat8")) {
        return "8";
      }
    }
    return null;
  }

  /**
   * 从容器镜像字符串中解析Java版本
   * 例如: openjdk8, openjdk21, openjdk23, openjdk24, ali-dragonwell8
   */
  private String parseJavaVersion(String container0Image) {
    // 解析OpenJDK版本
    if (container0Image.contains("openjdk")) {
      if (container0Image.contains("openjdk24")) {
        return "24";
      } else if (container0Image.contains("openjdk23")) {
        return "23";
      } else if (container0Image.contains("openjdk21")) {
        return "21";
      } else if (container0Image.contains("openjdk8")) {
        return "8";
      }
    }

    // 解析Ali Dragonwell版本
    if (container0Image.contains("ali-dragonwell")) {
      if (container0Image.contains("ali-dragonwell8")) {
        return "8 (Ali Dragonwell)";
      }
    }

    return null;
  }
}
