package com.fxiaoke.idp.helper.model;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * K8s应用信息数据
 */
@Data
public class K8sApp {
  /**
   * 应用名称
   */
  private String name;

  /**
   * 应用ID
   */
  private Integer id;

  private String remark;

  private String department;

  /**
   * 应用类型
   */
  private String type;

  /**
   * 应用状态
   */
  private String status;

  /**
   * 镜像
   */
  private String image;

  /**
   * 副本数
   */
  private Integer replicas;

  /**
   * 可用副本数
   */
  private Integer availableReplicas;

  /**
   * 创建时间
   */
  private LocalDateTime createdTime;

  /**
   * 更新时间
   */
  private LocalDateTime updatedTime;

  /**
   * 标签
   */
  private Map<String, String> labels;

  /**
   * 注解
   */
  private Map<String, String> annotations;

  /**
   * 描述
   */
  private String description;
}
