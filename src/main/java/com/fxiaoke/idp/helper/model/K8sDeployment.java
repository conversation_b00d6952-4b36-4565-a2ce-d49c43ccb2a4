package com.fxiaoke.idp.helper.model;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * K8s部署信息数据
 */
@Data
public class K8sDeployment {
  /**
   * 集群名称
   */
  private String cluster;
  
  /**
   * 命名空间
   */
  private String namespace;
  
  /**
   * 部署名称
   */
  private String name;
  
  /**
   * 应用名称
   */
  private String app;
  
  /**
   * 镜像
   */
  private String image;
  
  /**
   * 副本数
   */
  private Integer replicas;
  
  /**
   * 可用副本数
   */
  private Integer availableReplicas;
  
  /**
   * 就绪副本数
   */
  private Integer readyReplicas;
  
  /**
   * 状态
   */
  private String status;
  
  /**
   * 创建时间
   */
  private LocalDateTime createdTime;
  
  /**
   * 更新时间
   */
  private LocalDateTime updatedTime;
  
  /**
   * 标签
   */
  private Map<String, String> labels;
  
  /**
   * 注解
   */
  private Map<String, String> annotations;
}
