package com.fxiaoke.idp.helper.model;

import io.protostuff.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 汇总应用情况，上报到 biz log
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppReportBizLog {

  /**
   * 日志类型是generic-biz-log的要求，必须全局唯一，不能随便变
   */
  @Tag(1)
  private String logType = "app-jvm";
  @Tag(2)
  private long stamp;
  @Tag(3)
  private String app;
  @Tag(4)
  private String serverIp;
  @Tag(5)
  private String profile;
  @Tag(6)
  private String ea;
  @Tag(7)
  private String tenantId;
  @Tag(8)
  private String uid;
  @Tag(9)
  private String traceId;
  @Tag(10)
  private String smsType;
  @Tag(11)
  private String phone;
  @Tag(14)
  private String message;

  /**
   * 应用名称,注意Tag的序列参考biz-log说明，51-70是string类型的扩展字段
   */
  @Tag(51)
  private String name;

  /**
   * 所属部门
   */
  @Tag(52)
  private String department;

  /**
   * 应用级别 (L0, L1, L2, L3)
   */
  @Tag(53)
  private String level;

  /**
   * 主要负责人
   */
  @Tag(54)
  private String mainOwner;

  /**
   * 部门leader
   */
  @Tag(55)
  private String leader;

  /**
   * 集群名称
   */
  @Tag(56)
  private String cluster;

  /**
   * 命名空间
   */
  @Tag(57)
  private String namespace;

  @Tag(58)
  private String javaVersion;

  @Tag(59)
  private String tomcatVersion;

  /**
   * 当前运行副本数
   */
  @Tag(151)
  private Integer pipelineReplicas;

  /**
   * 发布系统配置的副本数
   */
  @Tag(152)
  private Integer replicas;

}

