package com.fxiaoke.idp.helper.task;

import com.fxiaoke.idp.helper.service.K8sService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 统计App状态
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AppReportTask {

  private final K8sService service;

  @Scheduled(cron = "${sharecrm.api.app.report.task.cron:0 0 5 * * ?}")
  public void doReport() {
    service.doReport();
  }

}
