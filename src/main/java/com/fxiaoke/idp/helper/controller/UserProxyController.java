package com.fxiaoke.idp.helper.controller;

import com.facishare.organization.api.model.department.arg.GetAllDepartmentDtoArg;
import com.facishare.organization.api.model.department.arg.GetDepartmentDtoArg;
import com.facishare.organization.api.model.department.result.GetAllDepartmentDtoResult;
import com.facishare.organization.api.model.department.result.GetDepartmentDtoResult;
import com.facishare.organization.api.model.employee.arg.GetAllEmployeesDtoArg;
import com.facishare.organization.api.model.employee.arg.GetEmployeeDtoArg;
import com.facishare.organization.api.model.employee.arg.GetEmployeeDtoLeaderArg;
import com.facishare.organization.api.model.employee.arg.GetEmployeesDtoByNameArg;
import com.facishare.organization.api.model.employee.result.GetAllEmployeesDtoResult;
import com.facishare.organization.api.model.employee.result.GetEmployeeDtoLeaderResult;
import com.facishare.organization.api.model.employee.result.GetEmployeeDtoResult;
import com.facishare.organization.api.model.employee.result.GetEmployeesDtoByNameResult;
import com.fxiaoke.idp.helper.model.FsVerifyPasswordArg;
import com.fxiaoke.idp.helper.model.FsVerifyPasswordResult;
import com.fxiaoke.idp.helper.service.FsLoginClient;
import com.fxiaoke.idp.helper.service.FsOrgClient;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 纷享自己的用户、组织管理接口。我们只在线上有用户接口，主要用于线下代理到线上。
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/public/fs-users")
public class UserProxyController {

  private final FsLoginClient fsLoginClient;
  private final FsOrgClient fsOrgClient;






  @PostMapping(value = "/EmployeeLoginService/verifyEmployeePasswordByUserAccount")
  public FsVerifyPasswordResult verifyUserAccount(@RequestBody FsVerifyPasswordArg arg) {
    return fsLoginClient.verifyUserAccount(arg);
  }

  @PostMapping(value = "/EmployeeProviderService/getAllEmployees")
  public GetAllEmployeesDtoResult getAllEmployees(@RequestBody GetAllEmployeesDtoArg arg) {
    return fsOrgClient.getAllEmployees(arg);
  }

  @PostMapping(value = "/EmployeeProviderService/getEmployeesByName")
  public GetEmployeesDtoByNameResult getEmployeesByName(@RequestBody GetEmployeesDtoByNameArg arg) {
    return fsOrgClient.getEmployeesByName(arg);
  }

  @PostMapping(value = "/EmployeeProviderService/getEmployeeDto")
  public GetEmployeeDtoResult getEmployeeDto(@RequestBody GetEmployeeDtoArg arg) {
    return fsOrgClient.getEmployeeDto(arg);
  }

  @PostMapping(value = "/EmployeeProviderService/getEmployeeDtoLeader")
  public GetEmployeeDtoLeaderResult getEmployeeDtoLeader(@RequestBody GetEmployeeDtoLeaderArg arg) {
    return fsOrgClient.getEmployeeDtoLeader(arg);
  }

  @PostMapping(value = "/DepartmentProviderService/getAllDepartmentDto")
  public GetAllDepartmentDtoResult getAllDepartmentDto(@RequestBody GetAllDepartmentDtoArg arg) {
    return fsOrgClient.getAllDepartmentDto(arg);
  }

  @PostMapping(value = "/DepartmentProviderService/getDepartmentDto")
  public GetDepartmentDtoResult getDepartmentDto(@RequestBody GetDepartmentDtoArg arg) {
    return fsOrgClient.getDepartmentDto(arg);
  }

}
