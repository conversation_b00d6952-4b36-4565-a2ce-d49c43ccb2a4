package com.fxiaoke.idp.helper.controller;

import com.fxiaoke.idp.helper.model.K8sApp;
import com.fxiaoke.idp.helper.model.K8sDeployment;
import com.fxiaoke.idp.helper.service.K8sService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * K8s管理平台相关API
 */
@RestController
@RequestMapping("/public/k8s")
@RequiredArgsConstructor
public class K8sController {

  private final K8sService k8sService;

  /**
   * 查询应用列表
   *
   * @return 应用列表
   */
  @GetMapping("/apps")
  public ResponseEntity<List<K8sApp>> getAppList() {
    List<K8sApp> apps = k8sService.queryAppList();
    return ResponseEntity.ok(apps);
  }

  /**
   * 查询部署列表
   *
   * @param cluster   集群名称
   * @param namespace 命名空间
   * @return 部署列表
   */
  @GetMapping("/deployments")
  public ResponseEntity<List<K8sDeployment>> getDeploymentList(
      @RequestParam String cluster,
      @RequestParam String namespace) {
    List<K8sDeployment> deployments = k8sService.queryDeploymentList(cluster, namespace);
    return ResponseEntity.ok(deployments);
  }
}
