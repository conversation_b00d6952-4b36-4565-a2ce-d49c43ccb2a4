package com.fxiaoke.idp.helper.service;

import com.fxiaoke.idp.helper.model.AppReportBizLog;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * K8sService版本解析功能测试
 */
@SpringBootTest
@TestPropertySource(properties = {
    "sharecrm.api.idp.helper.update-user-enabled=false"
})
class K8sServiceVersionParseTest {

  @Test
  void testParseVersionsFromDifferentImages() {
    // 由于parseAndSetVersions是私有方法，我们需要通过反射或者创建一个测试用的公共方法
    // 这里我们先创建一个简单的测试，验证方法存在
    K8sService k8sService = new K8sService(null, null);

    // 这个测试主要验证类能正常实例化，具体的版本解析逻辑可以通过集成测试验证
    assertNotNull(k8sService);
    assertEquals("9", biz1.getTomcatVersion());
    assertEquals("21", biz1.getJavaVersion());

    // 测试用例2: Tomcat 8 + Ali Dragonwell 8
    AppReportBizLog biz2 = new AppReportBizLog();
    k8sService.parseAndSetVersions(biz2,
        "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat8:ali-dragonwell8-v250618");
    assertEquals("8", biz2.getTomcatVersion());
    assertEquals("8 (Ali Dragonwell)", biz2.getJavaVersion());

    // 测试用例3: Tomcat 10 + OpenJDK 24
    AppReportBizLog biz3 = new AppReportBizLog();
    k8sService.parseAndSetVersions(biz3,
        "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat10:openjdk24-v250618");
    assertEquals("10", biz3.getTomcatVersion());
    assertEquals("24", biz3.getJavaVersion());

    // 测试用例4: 旧格式 Tomcat 8 + OpenJDK 8
    AppReportBizLog biz4 = new AppReportBizLog();
    k8sService.parseAndSetVersions(biz4, "reg.foneshare.cn/base/fs-tomcat8:openjdk8");
    assertEquals("8", biz4.getTomcatVersion());
    assertEquals("8", biz4.getJavaVersion());

    // 测试用例5: OpenJDK 23
    AppReportBizLog biz5 = new AppReportBizLog();
    k8sService.parseAndSetVersions(biz5,
        "apdb-harbor.foneshare.cn:30040/foneshare-proxy/base/fs-tomcat10:openjdk23-v250527");
    assertEquals("10", biz5.getTomcatVersion());
    assertEquals("23", biz5.getJavaVersion());

    // 测试用例6: 空字符串
    AppReportBizLog biz6 = new AppReportBizLog();
    k8sService.parseAndSetVersions(biz6, "");
    assertNull(biz6.getTomcatVersion());
    assertNull(biz6.getJavaVersion());

    // 测试用例7: null
    AppReportBizLog biz7 = new AppReportBizLog();
    k8sService.parseAndSetVersions(biz7, null);
    assertNull(biz7.getTomcatVersion());
    assertNull(biz7.getJavaVersion());
  }
}
